import OpenAI from 'openai';
import { Base<PERSON><PERSON>rovider } from '../BaseLLMProvider';
import { ChatCompletionRequest, ChatCompletionResponse } from '../interfaces';
import { ModelRegistry } from '../ModelRegistry';

export class OpenAIProvider extends Base<PERSON><PERSON>rovider {
  name = 'openai';
  private client: OpenAI;

  constructor() {
    super();
    
    if (!process.env.OPENAI_API_KEY) {
      throw new Error('OPENAI_API_KEY environment variable is required');
    }

    this.client = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });
  }

  isConfigured(): boolean {
    return !!process.env.OPENAI_API_KEY;
  }

  getSupportedModels(): string[] {
    return ModelRegistry.getModelsForProvider('openai').map(m => m.id);
  }

  async createChatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse> {
    const modelConfig = ModelRegistry.getModel(request.model, 'openai');
    if (!modelConfig) {
      throw new Error(`Unsupported model: ${request.model} for OpenAI provider`);
    }

    // o3-mini uses max_completion_tokens instead of max_tokens
    const isO3Model = modelConfig.providerModel.includes('o3');
    const tokenParams = isO3Model
      ? { max_completion_tokens: request.maxTokens }
      : { max_tokens: request.maxTokens };

    const completion = await this.client.chat.completions.create({
      model: modelConfig.providerModel,
      messages: request.messages,
      temperature: request.temperature,
      ...tokenParams
    });

    const content = completion.choices[0]?.message?.content;
    if (!content) {
      throw new Error('No response from OpenAI');
    }

    return {
      content,
      usage: completion.usage ? {
        promptTokens: completion.usage.prompt_tokens,
        completionTokens: completion.usage.completion_tokens,
        totalTokens: completion.usage.total_tokens
      } : undefined
    };
  }
}
