import { Router, Request, Response } from 'express';
import <PERSON><PERSON> from 'joi';
import { ApiResponse } from '@rpa-project/shared';
import { setMasterKey, loadMasterKey } from '../utils/encryption';
import { LLMProviderFactory, ProviderType } from '../services/llm/LLMProviderFactory';
import { LLMService } from '../services/llm/LLMService';
import { requireAuth, requireAdmin } from '../middleware/auth';

const router = Router();

// Validation schema for master key
const masterKeySchema = Joi.object({
  masterKey: Joi.string().required().length(64).pattern(/^[0-9a-fA-F]+$/)
});

// Validation schema for LLM provider settings
const llmProviderSchema = Joi.object({
  provider: Joi.string().valid('openai', 'azure').required(),
  defaultModel: Joi.string().optional()
});

// GET /api/settings/master-key/status - Check if master key exists (requires authentication)
router.get('/master-key/status', requireAuth, async (req: Request, res: Response) => {
  try {
    const masterKey = loadMasterKey();
    const exists = masterKey !== null;

    const response: ApiResponse = {
      success: true,
      data: {
        exists,
        message: exists ? 'Master key is configured' : 'Master key not configured'
      }
    };

    res.json(response);
  } catch (error) {
    console.error('Error checking master key status:', error);
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
    res.status(500).json(response);
  }
});

// POST /api/settings/master-key - Update master key (requires admin privileges)
router.post('/master-key', requireAuth, requireAdmin, async (req: Request, res: Response) => {
  try {
    const { error, value } = masterKeySchema.validate(req.body);
    if (error) {
      const response: ApiResponse = {
        success: false,
        error: 'Master key must be exactly 64 hexadecimal characters'
      };
      return res.status(400).json(response);
    }

    const { masterKey } = value;

    // Security check: Only allow setting master key if none exists
    const existingKey = loadMasterKey();
    if (existingKey !== null) {
      const response: ApiResponse = {
        success: false,
        error: 'Master key already exists and cannot be changed via API for security reasons'
      };
      return res.status(403).json(response);
    }

    // Set the new master key
    setMasterKey(masterKey);

    const response: ApiResponse = {
      success: true,
      data: { message: 'Master key updated successfully' }
    };

    res.json(response);
  } catch (error) {
    console.error('Error updating master key:', error);
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
    res.status(500).json(response);
  }
});

// GET /api/settings/llm-provider - Get current LLM provider configuration (requires authentication)
router.get('/llm-provider', requireAuth, async (req: Request, res: Response) => {
  try {
    const currentProvider = (process.env.LLM_PROVIDER as ProviderType) || 'openai';
    const defaultModel = LLMProviderFactory.getDefaultModel();

    const response: ApiResponse = {
      success: true,
      data: {
        provider: currentProvider,
        defaultModel,
        availableProviders: ['openai', 'azure']
      }
    };

    res.json(response);
  } catch (error) {
    console.error('Error getting LLM provider configuration:', error);
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
    res.status(500).json(response);
  }
});

// POST /api/settings/llm-provider - Update LLM provider configuration (requires admin privileges)
router.post('/llm-provider', requireAuth, requireAdmin, async (req: Request, res: Response) => {
  try {
    const { error, value } = llmProviderSchema.validate(req.body);
    if (error) {
      const response: ApiResponse = {
        success: false,
        error: 'Invalid LLM provider configuration'
      };
      return res.status(400).json(response);
    }

    const { provider, defaultModel } = value;

    // Update environment variables in runtime
    process.env.LLM_PROVIDER = provider;
    if (defaultModel) {
      process.env.LLM_DEFAULT_MODEL = defaultModel;
    }

    // Reset the LLM provider factory to pick up new configuration
    LLMProviderFactory.reset();

    // Test the new provider configuration
    try {
      const newProvider = LLMProviderFactory.getInstance();
      if (!newProvider.isConfigured()) {
        throw new Error(`Provider ${provider} is not properly configured`);
      }

      // Test connection to ensure the provider works
      const testResult = await newProvider.testConnection();
      if (!testResult) {
        throw new Error(`Failed to connect to ${provider} provider`);
      }

      const response: ApiResponse = {
        success: true,
        data: {
          message: `LLM provider successfully switched to ${provider}`,
          provider,
          defaultModel: defaultModel || process.env.LLM_DEFAULT_MODEL,
          testConnection: true
        }
      };

      res.json(response);
    } catch (providerError) {
      // Rollback on failure
      process.env.LLM_PROVIDER = (process.env.LLM_PROVIDER as ProviderType) || 'openai';
      LLMProviderFactory.reset();

      const response: ApiResponse = {
        success: false,
        error: `Failed to switch to ${provider}: ${providerError instanceof Error ? providerError.message : 'Unknown error'}`
      };
      res.status(400).json(response);
    }
  } catch (error) {
    console.error('Error updating LLM provider configuration:', error);
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
    res.status(500).json(response);
  }
});

// POST /api/settings/llm-provider/test - Test current LLM provider connection (requires authentication)
router.post('/llm-provider/test', requireAuth, async (req: Request, res: Response) => {
  try {
    const testResult = await LLMService.testConnection();
    const provider = LLMProviderFactory.getInstance();

    const response: ApiResponse = {
      success: testResult.success,
      data: {
        provider: testResult.provider,
        configured: provider.isConfigured(),
        connectionTest: testResult.success,
        supportedModels: provider.getSupportedModels(),
        error: testResult.error,
        message: testResult.success ?
          `Successfully connected to ${testResult.provider} provider` :
          `Failed to connect to ${testResult.provider} provider: ${testResult.error}`
      }
    };

    res.json(response);
  } catch (error) {
    console.error('Error testing LLM provider:', error);
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
    res.status(500).json(response);
  }
});

export { router as settingsRoutes };
