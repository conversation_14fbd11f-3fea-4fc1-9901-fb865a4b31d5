# Tools för RPA-projektet

Detta är verktyg och skript för att hantera utveckling och testdata i RPA-projektet.

## Seeding av testkunder

### Översikt

Skriptet `seed-customers.js` skapar realistiska testkunder för utveckling och testning. Det genererar svenska företagsnamn med olika branscher och företagsformer.

### Användning

#### Grundläggande användning
```bash
# Skapa 100 testkunder (standard)
npm run seed:customers

# Eller direkt med node
node tools/seed-customers.js
```

#### Avancerade alternativ
```bash
# Rensa befintliga testkunder och skapa 100 nya
npm run seed:customers:clear

# Skapa endast 50 testkunder
npm run seed:customers:50

# Anpassad användning med flaggor
node tools/seed-customers.js --clear --count 25
```

### Flaggor

- `--clear`: Rensar befintliga testkunder innan nya skapas
- `--count N`: Anger antal kunder att skapa (standard: 100)

### Vad skapas

Skriptet genererar kunder med:

- **Kundnummer**: 5-siffrigt slumpmässigt nummer (10000-99999)
- **Företagsnamn**: Kombination av svenska orter, branscher och företagsformer
  - Exempel: "Stockholms Teknik AB", "Göteborgs Bygg HB", "Malmö IT Konsult"
- **Visma-nummer**: 50% chans för ett V-prefixat 6-siffrigt nummer
- **Tidsstämplar**: Automatiska created_at och updated_at

### Exempel på genererade företagsnamn

- Nordiska Bygg AB
- Stockholms IT Konsult
- Göteborgs Transport HB
- Uppsala Fastighet Aktiebolag
- Malmö Design Enskild Firma
- Västerås Teknik Ekonomisk Förening

### Säkerhet

- Skriptet använder samma databas-konfiguration som backend
- Alla testkunder har numeriska kundnummer för enkel identifiering
- `--clear` flaggan tar endast bort kunder med numeriska kundnummer

### Tekniska detaljer

- Använder SQLite med better-sqlite3
- Transaktioner för optimal prestanda
- Hantering av unika kundnummer-konflikter
- WAL-läge för bättre prestanda

### Felsökning

#### "Customers-tabellen finns inte"
Kör backend först för att skapa databas-schemat:
```bash
cd backend
npm run dev
```

#### Databas-låsning
Stäng backend-servern innan du kör seeding:
```bash
# Stoppa backend, kör seeding, starta backend igen
```

#### Kundnummer-konflikter
Skriptet hanterar automatiskt konflikter genom att generera nya nummer.

## Andra verktyg

### generate-step.js
Verktyg för att generera nya steg-typer för RPA-flöden.

```bash
npm run generate:step
```

## Utveckling

### Lägga till nya verktyg

1. Skapa skript i `tools/` mappen
2. Lägg till npm-skript i root `package.json`
3. Dokumentera i denna README

### Kodstil

- Använd svenska kommentarer för användarmeddelanden
- Engelska för teknisk kod och variabler
- Emoji för visuell feedback i konsolen
- Felhantering med tydliga meddelanden
