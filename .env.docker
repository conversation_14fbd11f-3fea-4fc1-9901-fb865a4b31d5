# Docker Environment Configuration
# Copy this file to .env.docker and update the values for your Docker deployment

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
NODE_ENV=production
PORT=44300
USE_HTTPS=true

# =============================================================================
# REDIS CONFIGURATION (Docker Compose)
# =============================================================================
# When using docker-compose, Redis runs in a separate container
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_URL=redis://redis:6379

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# SQLite database path inside container
DB_PATH=/app/data/rpa.db

# =============================================================================
# ENCRYPTION CONFIGURATION
# =============================================================================
# Master key path inside container
MASTER_KEY_PATH=/app/data/master.key

# =============================================================================
# PLAYWRIGHT CONFIGURATION
# =============================================================================
PLAYWRIGHT_HEADLESS=true
PLAYWRIGHT_BROWSER=chromium
DOCKER_ENV=true

# =============================================================================
# QUEUE CONFIGURATION
# =============================================================================
QUEUE_NAME=rpa-flows
QUEUE_CONCURRENCY=5

# =============================================================================
# LLM PROVIDER CONFIGURATION
# =============================================================================
LLM_PROVIDER=openai                   # 'openai' | 'azure'
LLM_CHAT_ASSISTANT_MODEL=gpt-4o-mini  # Model for flow chat assistant (always 4o-mini)
LLM_AI_STEP_MODELS=gpt-4o-mini,o3-mini # Available models for AI steps (comma-separated)
LLM_FALLBACK_MODEL=gpt-4o-mini         # Fallback model if selected model fails

# =============================================================================
# OPENAI CONFIGURATION
# =============================================================================
OPENAI_API_KEY=********************************************************************************************************************************************************************

# =============================================================================
# AZURE OPENAI CONFIGURATION
# =============================================================================
AZURE_OPENAI_API_KEY=BMK63NxOfL9Wkv4mgmNCSNQ1UYEGhAqttoHaW7SUMsaucRHe1vghJQQJ99BGACfhMk5XJ3w3AAAAACOG59zn
AZURE_OPENAI_ENDPOINT=https://ai-api-services.cognitiveservices.azure.com/
AZURE_OPENAI_API_VERSION=2025-01-01-preview

# Azure Model Deployments
AZURE_MODEL_GPT4O_MINI=gpt-4o-mini
AZURE_MODEL_O1_MINI=o1-mini-deployment

# =============================================================================
# OAUTH2 CONFIGURATION (Docker)
# =============================================================================
# NOTE: For Docker deployment, you may need to update redirect URIs
# to match your actual domain/IP instead of localhost

# eEkonomi (Visma) OAuth2 Settings
EEKONOMI_CLIENT_ID=ebitappssverigeab
EEKONOMI_CLIENT_SECRET="BR-3tyEp8tvUNsfoS$YQx0k*NnIMeyRLvPabub0nx8frbLFXtEfLB4VIzbdbUViw"
# For Docker: Update this to your actual domain
EEKONOMI_REDIRECT_URI=https://localhost:44300/callback

# Fortnox OAuth2 Settings  
FORTNOX_CLIENT_ID=T4f7JxNlYQJC
FORTNOX_CLIENT_SECRET=6TWH8EBTik
# For Docker: Update this to your actual domain
FORTNOX_REDIRECT_URI=https://localhost:3002/api/oauth2/callback/Fortnox

# =============================================================================
# DOCKER-SPECIFIC SETTINGS
# =============================================================================
# Container timezone
TZ=Europe/Stockholm

# File upload limits
MAX_FILE_SIZE=10mb

# Security settings
HELMET_ENABLED=true
CORS_ORIGIN=*

# =============================================================================
# PRODUCTION DEPLOYMENT NOTES
# =============================================================================
# 1. Update OAuth2 redirect URIs to match your production domain
# 2. Set strong passwords for Redis if exposing to internet
# 3. Consider using Docker secrets for sensitive values
# 4. Update CORS_ORIGIN to your frontend domain in production
# 5. Ensure proper SSL/TLS termination at load balancer level
# Docker Environment Configuration
# Copy this file to .env.docker and update the values for your Docker deployment

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
NODE_ENV=production
PORT=3002

# =============================================================================
# REDIS CONFIGURATION (Docker Compose)
# =============================================================================
# When using docker-compose, Redis runs in a separate container
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_URL=redis://redis:6379

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# SQLite database path inside container
DB_PATH=/app/data/rpa.db

# =============================================================================
# ENCRYPTION CONFIGURATION
# =============================================================================
# Master key path inside container
MASTER_KEY_PATH=/app/data/master.key

# =============================================================================
# PLAYWRIGHT CONFIGURATION
# =============================================================================
PLAYWRIGHT_HEADLESS=true
PLAYWRIGHT_BROWSER=chromium

# =============================================================================
# QUEUE CONFIGURATION
# =============================================================================
QUEUE_NAME=rpa-flows
QUEUE_CONCURRENCY=5

# =============================================================================
# LLM PROVIDER CONFIGURATION
# =============================================================================
LLM_PROVIDER=openai                   # 'openai' | 'azure'
LLM_CHAT_ASSISTANT_MODEL=gpt-4o-mini  # Model for flow chat assistant (always 4o-mini)
LLM_AI_STEP_MODELS=gpt-4o-mini,o3-mini # Available models for AI steps (comma-separated)
LLM_FALLBACK_MODEL=gpt-4o-mini         # Fallback model if selected model fails

# =============================================================================
# OPENAI CONFIGURATION
# =============================================================================
OPENAI_API_KEY=********************************************************************************************************************************************************************

# =============================================================================
# AZURE OPENAI CONFIGURATION
# =============================================================================
AZURE_OPENAI_API_KEY=BMK63NxOfL9Wkv4mgmNCSNQ1UYEGhAqttoHaW7SUMsaucRHe1vghJQQJ99BGACfhMk5XJ3w3AAAAACOG59zn
AZURE_OPENAI_ENDPOINT=https://ai-api-services.cognitiveservices.azure.com/
AZURE_OPENAI_API_VERSION=2025-01-01-preview

# Azure Model Deployments
AZURE_MODEL_GPT4O_MINI=gpt-4o-mini
AZURE_MODEL_O1_MINI=o1-mini-deployment

# =============================================================================
# OAUTH2 CONFIGURATION (Docker)
# =============================================================================
# NOTE: For Docker deployment, you may need to update redirect URIs
# to match your actual domain/IP instead of localhost

# eEkonomi (Visma) OAuth2 Settings
EEKONOMI_CLIENT_ID=ebitappssverigeab
EEKONOMI_CLIENT_SECRET="BR-3tyEp8tvUNsfoS$YQx0k*NnIMeyRLvPabub0nx8frbLFXtEfLB4VIzbdbUViw"
# For Docker: Update this to your actual domain
EEKONOMI_REDIRECT_URI=http://localhost:3002/api/oauth2/callback/eEkonomi

# Fortnox OAuth2 Settings  
FORTNOX_CLIENT_ID=T4f7JxNlYQJC
FORTNOX_CLIENT_SECRET=6TWH8EBTik
# For Docker: Update this to your actual domain
FORTNOX_REDIRECT_URI=http://localhost:3002/api/oauth2/callback/Fortnox

# =============================================================================
# DOCKER-SPECIFIC SETTINGS
# =============================================================================
# Container timezone
TZ=Europe/Stockholm

# File upload limits
MAX_FILE_SIZE=10mb

# Security settings
HELMET_ENABLED=true
CORS_ORIGIN=*

# =============================================================================
# PRODUCTION DEPLOYMENT NOTES
# =============================================================================
# 1. Update OAuth2 redirect URIs to match your production domain
# 2. Set strong passwords for Redis if exposing to internet
# 3. Consider using Docker secrets for sensitive values
# 4. Update CORS_ORIGIN to your frontend domain in production
# 5. Ensure proper SSL/TLS termination at load balancer level
