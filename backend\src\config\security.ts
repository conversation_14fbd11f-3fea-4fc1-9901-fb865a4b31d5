/**
 * Security configuration for the RPA application
 */

export interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Max requests per window
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

export interface SecurityConfig {
  jwt: {
    secret: string;
    expiresIn: string;
    refreshExpiresIn: string;
  };
  rateLimits: {
    global: RateLimitConfig;
    auth: RateLimitConfig;
    api: RateLimitConfig;
  };
  security: {
    bcryptRounds: number;
    maxLoginAttempts: number;
    lockoutDuration: number; // in minutes
    sessionTimeout: number; // in minutes
    requireHttps: boolean;
    allowedOrigins: string[];
    ipWhitelist?: string[];
  };
}
export const securityConfig: SecurityConfig = {
  jwt: {
    secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production',
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
  },
  
  rateLimits: {
    // Global rate limit for all API requests
    global: {
      windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
      maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '1000'), // 1000 requests per 15 min
      skipSuccessfulRequests: false,
      skipFailedRequests: false,
    },
    
    // Stricter rate limit for authentication endpoints
    auth: {
      windowMs: parseInt(process.env.AUTH_RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
      maxRequests: parseInt(process.env.AUTH_RATE_LIMIT_MAX_REQUESTS || '10'), // 10 login attempts per 15 min
      skipSuccessfulRequests: true,
      skipFailedRequests: false,
    },
    
    // Rate limit for API key usage
    api: {
      windowMs: parseInt(process.env.API_RATE_LIMIT_WINDOW_MS || '60000'), // 1 minute
      maxRequests: parseInt(process.env.API_RATE_LIMIT_MAX_REQUESTS || '100'), // 100 requests per minute
      skipSuccessfulRequests: false,
      skipFailedRequests: false,
    },
  },
  
  security: {
    bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS || '12'),
    maxLoginAttempts: parseInt(process.env.MAX_LOGIN_ATTEMPTS || '5'),
    lockoutDuration: parseInt(process.env.LOCKOUT_DURATION || '30'), // 30 minutes
    sessionTimeout: parseInt(process.env.SESSION_TIMEOUT || '1440'), // 24 hours in minutes
    requireHttps: process.env.NODE_ENV === 'production',
    allowedOrigins: process.env.ALLOWED_ORIGINS?.split(',') || [
      'http://localhost:3000',
      'http://localhost:3001',
      'https://localhost:3000',
      'https://localhost:3001',
    ],
    ipWhitelist: process.env.IP_WHITELIST?.split(','), // Optional IP whitelist
  },
};

/**
 * Validate security configuration
 */
export function validateSecurityConfig(): void {
  const errors: string[] = [];

  // Validate JWT secret
  if (!securityConfig.jwt.secret || securityConfig.jwt.secret.length < 32) {
    errors.push('JWT_SECRET must be at least 32 characters long');
  }

  // Validate bcrypt rounds
  if (securityConfig.security.bcryptRounds < 10 || securityConfig.security.bcryptRounds > 15) {
    errors.push('BCRYPT_ROUNDS should be between 10 and 15');
  }

  // Validate rate limits
  if (securityConfig.rateLimits.global.maxRequests <= 0) {
    errors.push('Global rate limit max requests must be greater than 0');
  }

  if (securityConfig.rateLimits.auth.maxRequests <= 0) {
    errors.push('Auth rate limit max requests must be greater than 0');
  }

  // Validate session timeout
  if (securityConfig.security.sessionTimeout <= 0) {
    errors.push('Session timeout must be greater than 0');
  }

  // Validate lockout duration
  if (securityConfig.security.lockoutDuration <= 0) {
    errors.push('Lockout duration must be greater than 0');
  }

  if (errors.length > 0) {
    throw new Error(`Security configuration validation failed:\n${errors.join('\n')}`);
  }
}

/**
 * Get environment-specific security settings
 */
export function getEnvironmentSecuritySettings() {
  const isDevelopment = process.env.NODE_ENV === 'development';
  const isProduction = process.env.NODE_ENV === 'production';

  return {
    isDevelopment,
    isProduction,
    logLevel: isDevelopment ? 'debug' : 'info',
    enableDetailedErrors: isDevelopment,
    enableSecurityHeaders: isProduction,
    enableHttpsRedirect: isProduction,
    enableCsrfProtection: isProduction,
    cookieSecure: isProduction,
    cookieSameSite: isProduction ? 'strict' : 'lax',
  };
}

/**
 * Security headers configuration
 */
export const securityHeaders = {
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  hsts: {
    maxAge: 31536000, // 1 year
    includeSubDomains: true,
    preload: true,
  },
  noSniff: true,
  frameguard: { action: 'deny' as const },
  xssFilter: true,
  referrerPolicy: { policy: 'strict-origin-when-cross-origin' as const },
};

/**
 * CORS configuration
 */
export const corsConfig = {
  origin: (origin: string | undefined, callback: (err: Error | null, allow?: boolean) => void) => {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);
    
    if (securityConfig.security.allowedOrigins.includes(origin)) {
      return callback(null, true);
    }
    
    return callback(new Error('Not allowed by CORS'));
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'X-API-Key',
    'X-CSRF-Token',
  ],
  exposedHeaders: [
    'X-RateLimit-Limit',
    'X-RateLimit-Remaining',
    'X-RateLimit-Reset',
  ],
  maxAge: 86400, // 24 hours
};

/**
 * Cookie configuration
 */
export const cookieConfig = {
  httpOnly: true,
  secure: getEnvironmentSecuritySettings().cookieSecure,
  sameSite: getEnvironmentSecuritySettings().cookieSameSite as 'strict' | 'lax',
  maxAge: securityConfig.security.sessionTimeout * 60 * 1000, // Convert minutes to milliseconds
  domain: process.env.COOKIE_DOMAIN,
  path: '/',
};

/**
 * API key configuration
 */
export const apiKeyConfig = {
  keyLength: 64, // Length of generated API keys
  hashRounds: 12, // bcrypt rounds for hashing API keys
  defaultExpirationDays: 365, // Default expiration for API keys (1 year)
  maxKeysPerUser: 10, // Maximum API keys per user
  headerName: 'X-API-Key', // Header name for API key authentication
  prefixes: {
    user: 'rpa_user_',
    api: 'rpa_api_',
    admin: 'rpa_admin_',
  },
};

/**
 * Password policy configuration
 */
export const passwordPolicy = {
  minLength: 8,
  maxLength: 128,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSpecialChars: true,
  specialChars: '!@#$%^&*()_+-=[]{}|;:,.<>?',
  preventCommonPasswords: true,
  preventUserInfoInPassword: true,
};

/**
 * Validate password against policy
 */
export function validatePassword(password: string, userInfo?: { username?: string; email?: string }): string[] {
  const errors: string[] = [];

  if (password.length < passwordPolicy.minLength) {
    errors.push(`Password must be at least ${passwordPolicy.minLength} characters long`);
  }

  if (password.length > passwordPolicy.maxLength) {
    errors.push(`Password must be no more than ${passwordPolicy.maxLength} characters long`);
  }

  if (passwordPolicy.requireUppercase && !/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }

  if (passwordPolicy.requireLowercase && !/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }

  if (passwordPolicy.requireNumbers && !/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }

  if (passwordPolicy.requireSpecialChars) {
    const specialCharsRegex = new RegExp(`[${passwordPolicy.specialChars.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}]`);
    if (!specialCharsRegex.test(password)) {
      errors.push(`Password must contain at least one special character: ${passwordPolicy.specialChars}`);
    }
  }

  // Check if password contains user information
  if (passwordPolicy.preventUserInfoInPassword && userInfo) {
    const lowerPassword = password.toLowerCase();
    if (userInfo.username && lowerPassword.includes(userInfo.username.toLowerCase())) {
      errors.push('Password cannot contain username');
    }
    if (userInfo.email) {
      const emailParts = userInfo.email.toLowerCase().split('@');
      if (emailParts[0] && lowerPassword.includes(emailParts[0])) {
        errors.push('Password cannot contain email address');
      }
    }
  }

  return errors;
}

/**
 * Common weak passwords to prevent
 */
export const commonWeakPasswords = [
  'password', 'password123', '123456', '123456789', 'qwerty', 'abc123',
  'password1', 'admin', 'administrator', 'root', 'user', 'guest',
  'welcome', 'login', 'passw0rd', 'p@ssw0rd', 'p@ssword',
];

/**
 * Check if password is in common weak passwords list
 */
export function isCommonWeakPassword(password: string): boolean {
  return commonWeakPasswords.includes(password.toLowerCase());
}
