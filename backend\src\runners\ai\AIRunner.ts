import { RpaFlow, RpaStep, FlowSettings, ExecutionLog } from '@rpa-project/shared';
import { <PERSON><PERSON><PERSON><PERSON>, Runner<PERSON>ontext, StepExecutionResult } from '../base';
import { STEP_RUNNER_MAPPING } from '../registry/stepTypes';
import {
  executeProcessWithLLM,
  executeExtractPdfValues,
  executeScript
} from './stepExecutors';

/**
 * Enhanced AIRunner with modular step executors
 * Handles AI-based processing steps like PDF text extraction and LLM processing
 */
export class AIRunner extends BaseRunner {
  constructor() {
    super();
    // Ingen OpenAI-specifik kod behövs här längre
  }

  async initialize(settings: FlowSettings, variables: Record<string, any> = {}): Promise<void> {
    // AI runner doesn't need special initialization
    // Variables are handled in the context
  }

  getSupportedStepTypes(): string[] {
    return Object.keys(STEP_RUNNER_MAPPING).filter(
      stepType => STEP_RUNNER_MAPPING[stepType as keyof typeof STEP_RUNNER_MAPPING] === 'ai'
    );
  }

  async executeStep(step: RpaStep, context: RunnerContext, stepIndex?: number): Promise<StepExecutionResult> {
    const { variables, onLog } = context;

    try {
      // Type assertion to handle the new step type until shared package is updated
      const stepType = (step as any).type;

      onLog({
        level: 'info',
        message: `Executing AI step: ${stepType}`,
        stepId: step.id
      });

      // Create executor context
      const executorContext = {
        variables: context.variables,
        onLog,
        interpolateVariables: this.interpolateVariables.bind(this)
        // openai tas bort - step executors använder LLMService direkt
      };

      // Route to appropriate step executor
      switch (stepType) {
        case 'extractPdfValues':
          return await executeExtractPdfValues(step as any, executorContext, stepIndex);

        case 'processWithLLM':
          return await executeProcessWithLLM(step as any, executorContext, stepIndex);

        case 'script':
          return await executeScript(step as any, executorContext, stepIndex);

        default:
          return {
            success: false,
            error: `Unsupported step type: ${stepType}`
          };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const stepType = (step as any).type;
      onLog({
        level: 'error',
        message: `Error executing step ${stepType}: ${errorMessage}`,
        stepId: step.id
      });

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  async cleanup(): Promise<void> {
    // AI runner doesn't need cleanup
  }
}
