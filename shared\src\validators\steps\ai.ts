import { RpaStep, ExtractPdfValuesStep, ProcessWithLLMStep, ScriptStep } from '../../types/steps';
import { ValidationResult, ValidationError, createStepFromType } from '../../utils';

export function validateAiStep(step: RpaStep): ValidationResult {
  const errors: ValidationError[] = [];

  switch (step.type) {
    case 'extractPdfValues':
      const pdfStep = step as ExtractPdfValuesStep;

      if (!pdfStep.base64Input || pdfStep.base64Input.trim() === '') {
        errors.push({
          field: 'base64Input',
          message: 'Base64 input is required for PDF values extraction',

        });
      }

      if (!pdfStep.prompt || pdfStep.prompt.trim() === '') {
        errors.push({
          field: 'prompt',
          message: 'Prompt is required for PDF values extraction',

        });
      }

      // model is optional - if not provided, a default will be used
      // variableName is optional - if not provided, a default will be used

      break;

    case 'processWithLLM':
      const llmStep = step as ProcessWithLLMStep;

      if (!llmStep.textInput || llmStep.textInput.trim() === '') {
        errors.push({
          field: 'textInput',
          message: 'Text input is required for LLM processing',
        });
      }

      if (!llmStep.prompt || llmStep.prompt.trim() === '') {
        errors.push({
          field: 'prompt',
          message: 'Prompt is required for LLM processing',
        });
      }

      // Validate temperature
      if (llmStep.temperature !== undefined && (llmStep.temperature < 0 || llmStep.temperature > 2)) {
        errors.push({
          field: 'temperature',
          message: 'Temperature must be between 0 and 2',
        });
      }

      // Validate maxTokens
      if (llmStep.maxTokens !== undefined && (llmStep.maxTokens < 1 || llmStep.maxTokens > 128000)) {
        errors.push({
          field: 'maxTokens',
          message: 'Max tokens must be between 1 and 128000',
        });
      }

      // model is optional - if not provided, a default will be used
      // variableName is optional - if not provided, a default will be used

      break;

    case 'script':
      const scriptStep = step as ScriptStep;

      if (!scriptStep.code || scriptStep.code.trim() === '') {
        errors.push({
          field: 'code',
          message: 'JavaScript code is required for script step',
        });
      }

      // Basic syntax validation - check for obvious syntax errors
      if (scriptStep.code && scriptStep.code.trim()) {
        try {
          // Try to parse the code to check for syntax errors
          new Function('variables', scriptStep.code);
        } catch (syntaxError) {
          errors.push({
            field: 'code',
            message: `JavaScript syntax error: ${syntaxError instanceof Error ? syntaxError.message : 'Unknown syntax error'}`,
          });
        }
      }

      // Validate timeout
      if (scriptStep.timeout !== undefined && (scriptStep.timeout < 1000 || scriptStep.timeout > 300000)) {
        errors.push({
          field: 'timeout',
          message: 'Timeout must be between 1000ms (1s) and 300000ms (5min)',
        });
      }

      // variableName is optional - if not provided, a default will be used

      break;
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

export function createAiStepFromType(stepType: string): RpaStep {
  switch (stepType) {
    case 'extractPdfText':
      return createStepFromType(stepType);
    default:
      throw new Error(`Not an AI step type: ${stepType}`);
  }
}
