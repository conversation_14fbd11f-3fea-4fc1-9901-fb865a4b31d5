import { RpaStep, ExtractPdfValuesStep, ScriptStep } from '../../types/steps';
import { ValidationResult, ValidationError, createStepFromType } from '../../utils';

export function validateAiStep(step: RpaStep): ValidationResult {
  const errors: ValidationError[] = [];

  switch (step.type) {
    case 'extractPdfValues':
      const pdfStep = step as ExtractPdfValuesStep;

      if (!pdfStep.base64Input || pdfStep.base64Input.trim() === '') {
        errors.push({
          field: 'base64Input',
          message: 'Base64 input is required for PDF values extraction',

        });
      }

      if (!pdfStep.prompt || pdfStep.prompt.trim() === '') {
        errors.push({
          field: 'prompt',
          message: 'Prompt is required for PDF values extraction',

        });
      }

      // variableName is optional - if not provided, a default will be used

      break;

    case 'script':
      const scriptStep = step as ScriptStep;

      if (!scriptStep.code || scriptStep.code.trim() === '') {
        errors.push({
          field: 'code',
          message: 'JavaScript code is required for script step',
        });
      }

      // Basic syntax validation - check for obvious syntax errors
      if (scriptStep.code && scriptStep.code.trim()) {
        try {
          // Try to parse the code to check for syntax errors
          new Function('variables', scriptStep.code);
        } catch (syntaxError) {
          errors.push({
            field: 'code',
            message: `JavaScript syntax error: ${syntaxError instanceof Error ? syntaxError.message : 'Unknown syntax error'}`,
          });
        }
      }

      // Validate timeout
      if (scriptStep.timeout !== undefined && (scriptStep.timeout < 1000 || scriptStep.timeout > 300000)) {
        errors.push({
          field: 'timeout',
          message: 'Timeout must be between 1000ms (1s) and 300000ms (5min)',
        });
      }

      // variableName is optional - if not provided, a default will be used

      break;
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

export function createAiStepFromType(stepType: string): RpaStep {
  switch (stepType) {
    case 'extractPdfText':
      return createStepFromType(stepType);
    default:
      throw new Error(`Not an AI step type: ${stepType}`);
  }
}
