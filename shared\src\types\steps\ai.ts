import { RpaStepBase } from './base';

// AI processing steps
export interface ExtractPdfValuesStep extends RpaStepBase {
  type: 'extractPdfValues';
  base64Input: string; // Base64 PDF data or variable reference
  prompt: string; // User prompt for LLM processing to extract specific values
  model?: string; // LLM model to use (gpt-4o-mini or o3-mini)
  variableName?: string; // Variable name to store the extracted JSON result (optional, defaults to 'extractedData')
}

// Process text with LLM step
export interface ProcessWithLLMStep extends RpaStepBase {
  type: 'processWithLLM';
  textInput: string; // Text to process or variable reference
  prompt: string; // User prompt for LLM processing
  model?: string; // LLM model to use (gpt-4o-mini or o3-mini)
  temperature?: number; // Temperature for AI response (default: 0.3)
  maxTokens?: number; // Max tokens for response (default: 2000)
  variableName?: string; // Variable name to store AI response (default: 'var-ai-response')
}

// Script processing step
export interface ScriptStep extends RpaStepBase {
  type: 'script';
  code: string; // JavaScript code to execute
  variableName?: string; // Variable name to store the script result (optional, defaults to 'scriptResult')
  timeout?: number; // Timeout in milliseconds (default: 60000)
}
