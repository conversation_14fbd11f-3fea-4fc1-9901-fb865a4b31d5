#!/usr/bin/env node

/**
 * Script för att skapa 100 testkunder och ladda in dem i databasen
 * Kör med: node tools/seed-customers.js
 */

const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');
const crypto = require('crypto');

// Databas-konfiguration (samma som i backend)
const DB_PATH = process.env.DB_PATH || path.join(process.cwd(), 'backend', 'data', 'rpa.db');
const DB_DIR = path.dirname(DB_PATH);

// Säkerställ att databas-mappen finns
if (!fs.existsSync(DB_DIR)) {
  fs.mkdirSync(DB_DIR, { recursive: true });
}

// Skapa databas-anslutning
const database = new Database(DB_PATH);

// Aktivera WAL-läge för bättre prestanda
database.pragma('journal_mode = WAL');

// Testdata för att generera realistiska kundnamn
const companyTypes = [
  'AB', 'Aktiebolag', 'HB', 'KB', 'Enskild Firma', 'Ekonomisk Förening',
  'Stiftelse', 'Ideell Förening', 'Handelsbolag', 'Kommanditbolag'
];

const companyNames = [
  'Nordiska', 'Svenska', 'Stockholms', 'Göteborgs', 'Malmö', 'Uppsala', 'Västerås',
  'Örebro', 'Linköpings', 'Helsingborgs', 'Jönköpings', 'Norrköpings', 'Lunds',
  'Umeå', 'Gävle', 'Borås', 'Eskilstuna', 'Halmstad', 'Växjö', 'Karlstad',
  'Sundsvall', 'Trollhättan', 'Östersund', 'Falun', 'Skövde', 'Karlskrona',
  'Kristianstad', 'Kalmar', 'Vänersborg', 'Lidköping', 'Sandviken', 'Varberg'
];

const businessSectors = [
  'Bygg', 'Teknik', 'Konsult', 'Handel', 'Transport', 'IT', 'Media', 'Design',
  'Fastighet', 'Finans', 'Juridik', 'Hälsa', 'Utbildning', 'Restaurang',
  'Hotell', 'Rese', 'Bil', 'Maskin', 'Elektronik', 'Textil', 'Möbler',
  'Trä', 'Metall', 'Plast', 'Glas', 'Papper', 'Kemisk', 'Läkemedel',
  'Energi', 'Miljö', 'Säkerhet', 'Rengöring', 'Catering', 'Event',
  'Foto', 'Video', 'Musik', 'Sport', 'Hobby', 'Trädgård', 'Djur'
];

// Funktion för att generera ett unikt ID
function generateId() {
  return crypto.randomBytes(16).toString('hex');
}

// Funktion för att generera ett slumpmässigt kundnummer
function generateCustomerNumber() {
  return Math.floor(10000 + Math.random() * 90000).toString();
}

// Funktion för att generera ett slumpmässigt företagsnamn
function generateCompanyName() {
  const name = companyNames[Math.floor(Math.random() * companyNames.length)];
  const sector = businessSectors[Math.floor(Math.random() * businessSectors.length)];
  const type = companyTypes[Math.floor(Math.random() * companyTypes.length)];
  
  return `${name} ${sector} ${type}`;
}

// Funktion för att generera ett slumpmässigt Visma-nummer
function generateVismaNumber() {
  // 50% chans att ha ett Visma-nummer
  if (Math.random() < 0.5) {
    return '';
  }
  return 'V' + Math.floor(100000 + Math.random() * 900000).toString();
}

// Funktion för att skapa en testkund
function createTestCustomer() {
  const now = new Date().toISOString();
  
  return {
    id: generateId(),
    customer_number: generateCustomerNumber(),
    name: generateCompanyName(),
    visma_number: generateVismaNumber(),
    created_at: now,
    updated_at: now
  };
}

// Funktion för att rensa testkunder (de som har kundnummer som börjar med siffror)
function clearTestCustomers() {
  console.log('🧹 Rensar befintliga testkunder...');

  // Ta bort kunder som har numeriska kundnummer (våra testdata)
  const deleteResult = database.prepare(`
    DELETE FROM customers
    WHERE customer_number GLOB '[0-9]*'
  `).run();

  console.log(`🗑️ Tog bort ${deleteResult.changes} testkunder`);
  return deleteResult.changes;
}

// Huvudfunktion för att seeda kunder
async function seedCustomers(options = {}) {
  const { clear = false, count = 100 } = options;

  console.log('🌱 Startar seeding av testkunder...');

  try {
    // Kontrollera om customers-tabellen finns
    const tableExists = database.prepare(`
      SELECT name FROM sqlite_master WHERE type='table' AND name='customers'
    `).get();
    
    if (!tableExists) {
      console.error('❌ Customers-tabellen finns inte. Kör backend först för att skapa tabellerna.');
      process.exit(1);
    }
    
    // Rensa befintliga testkunder om begärt
    if (clear) {
      clearTestCustomers();
    }

    // Räkna befintliga kunder
    const existingCount = database.prepare('SELECT COUNT(*) as count FROM customers').get().count;
    console.log(`📊 Befintliga kunder i databasen: ${existingCount}`);
    
    // Förbered insert-statement
    const insertCustomer = database.prepare(`
      INSERT INTO customers (id, customer_number, name, visma_number, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?)
    `);
    
    // Skapa en transaktion för bättre prestanda
    const insertMany = database.transaction((customers) => {
      for (const customer of customers) {
        try {
          insertCustomer.run(
            customer.id,
            customer.customer_number,
            customer.name,
            customer.visma_number,
            customer.created_at,
            customer.updated_at
          );
        } catch (error) {
          if (error.code === 'SQLITE_CONSTRAINT_UNIQUE') {
            // Kundnummer finns redan, generera ett nytt
            customer.customer_number = generateCustomerNumber();
            insertCustomer.run(
              customer.id,
              customer.customer_number,
              customer.name,
              customer.visma_number,
              customer.created_at,
              customer.updated_at
            );
          } else {
            throw error;
          }
        }
      }
    });
    
    // Generera testkunder
    console.log(`🏭 Genererar ${count} testkunder...`);
    const customers = [];
    for (let i = 0; i < count; i++) {
      customers.push(createTestCustomer());
    }
    
    // Infoga alla kunder i en transaktion
    console.log('💾 Sparar kunder i databasen...');
    insertMany(customers);
    
    // Verifiera att kunderna har skapats
    const newCount = database.prepare('SELECT COUNT(*) as count FROM customers').get().count;
    const addedCount = newCount - existingCount;
    
    console.log(`✅ Seeding klar!`);
    console.log(`📈 Totalt antal kunder nu: ${newCount}`);
    console.log(`➕ Nya kunder tillagda: ${addedCount}`);
    
    // Visa några exempel på de skapade kunderna
    console.log('\n📋 Exempel på skapade kunder:');
    const sampleCustomers = database.prepare(`
      SELECT customer_number, name, visma_number 
      FROM customers 
      ORDER BY created_at DESC 
      LIMIT 5
    `).all();
    
    sampleCustomers.forEach((customer, index) => {
      console.log(`${index + 1}. ${customer.customer_number} - ${customer.name}${customer.visma_number ? ` (Visma: ${customer.visma_number})` : ''}`);
    });
    
  } catch (error) {
    console.error('❌ Fel vid seeding av kunder:', error);
    process.exit(1);
  } finally {
    database.close();
  }
}

// Kör seeding om skriptet körs direkt
if (require.main === module) {
  // Hantera kommandoradsargument
  const args = process.argv.slice(2);
  const options = {};

  // Kontrollera för --clear flagga
  if (args.includes('--clear')) {
    options.clear = true;
  }

  // Kontrollera för --count flagga
  const countIndex = args.indexOf('--count');
  if (countIndex !== -1 && args[countIndex + 1]) {
    const count = parseInt(args[countIndex + 1]);
    if (!isNaN(count) && count > 0) {
      options.count = count;
    }
  }

  seedCustomers(options);
}

module.exports = { seedCustomers, clearTestCustomers };
