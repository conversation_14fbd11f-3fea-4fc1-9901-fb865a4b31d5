// Test script för att verifiera att LLM provider-byte fungerar i praktiken
require('dotenv').config();

const { LLMService } = require('../dist/services/llm/LLMService');
const { LLMProviderFactory } = require('../dist/services/llm/LLMProviderFactory');

const BASE_URL = 'http://localhost:3002/api/settings';

async function testLLMSwitching() {
  console.log('🧪 Testing LLM Provider Switching with Real Requests...');

  try {
    // Test 1: Make a request with OpenAI
    console.log('\n1. Testing OpenAI provider with real request...');
    await switchProvider('openai');
    const openaiResponse = await LLMService.createChatCompletion([
      { role: 'user', content: 'Säg "Hej från OpenAI" på svenska' }
    ], { maxTokens: 50 });
    console.log('OpenAI Response:', openaiResponse.content);
    console.log('OpenAI Usage:', openaiResponse.usage);

    // Test 2: Switch to Azure and make a request
    console.log('\n2. Switching to Azure provider...');
    await switchProvider('azure');
    console.log('Testing Azure provider with real request...');
    const azureResponse = await LLMService.createChatCompletion([
      { role: 'user', content: 'Säg "Hej från Azure" på svenska' }
    ], { maxTokens: 50 });
    console.log('Azure Response:', azureResponse.content);
    console.log('Azure Usage:', azureResponse.usage);

    // Test 3: Switch back to OpenAI and make another request
    console.log('\n3. Switching back to OpenAI provider...');
    await switchProvider('openai');
    console.log('Testing OpenAI provider again...');
    const openaiResponse2 = await LLMService.createChatCompletion([
      { role: 'user', content: 'Säg "Hej igen från OpenAI" på svenska' }
    ], { maxTokens: 50 });
    console.log('OpenAI Response 2:', openaiResponse2.content);
    console.log('OpenAI Usage 2:', openaiResponse2.usage);

    console.log('\n✅ All LLM switching tests passed!');

  } catch (error) {
    console.error('❌ LLM switching test failed:', error.message);
  }
}

async function switchProvider(provider) {
  const response = await fetch(`${BASE_URL}/llm-provider`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      provider: provider,
      defaultModel: 'gpt-4o-mini'
    })
  });
  
  const result = await response.json();
  if (!result.success) {
    throw new Error(`Failed to switch to ${provider}: ${result.error}`);
  }
  
  console.log(`✅ Successfully switched to ${provider} provider`);
  return result;
}

// Wait a bit for server to be ready, then run tests
setTimeout(testLLMSwitching, 2000);
