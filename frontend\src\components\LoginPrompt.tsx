import React, { useState } from 'react';
import { authService } from '../services/auth';

interface LoginPromptProps {
  onLogin: () => void;
}

export const LoginPrompt: React.FC<LoginPromptProps> = ({ onLogin }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [username, setUsername] = useState('admin');
  const [password, setPassword] = useState('admin123!');
  const [error, setError] = useState<string | null>(null);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    
    try {
      await authService.login(username, password);

      onLogin();
    } catch (error: any) {
      console.error('Login failed:', error);
      setError(error.message || 'Inloggning misslyckades');
    } finally {
      setIsLoading(false);
    }
  };

  // Removed unused handleQuickLogin function

  return (
    <div className="login-page">
      <div className="login-container">
        {/* Left side - Login form */}
        <div className="login-form-section">
          <div className="login-form-container">
            <div className="login-header">
              <div className="login-logo">
                <h1 className="login-title">Automation Hub</h1>
                <div className="login-logo-icon">
                  <img height={48} width={48} src="data:image/png;base64,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" alt="" />
                </div>
              </div>
              <div className="login-subtitle">
                <h2>Välkommen tillbaka</h2>
              </div>
            </div>

            <form onSubmit={handleLogin} className="login-form">
              {error && (
                <div className="login-error">
                  <div className="login-error-icon">
                    <svg viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <p>{error}</p>
                </div>
              )}
              
              <div className="login-form-fields">
                <div className="form-group">
                  <label htmlFor="username" className="form-label">Användarnamn</label>
                  <div className="login-input-container">
                    <div className="login-input-icon">
                      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                    </div>
                    <input
                      id="username"
                      type="text"
                      value={username}
                      onChange={(e) => setUsername(e.target.value)}
                      className="form-input login-input"
                      placeholder="Ange ditt användarnamn"
                      required
                    />
                  </div>
                </div>
                
                <div className="form-group">
                  <label htmlFor="password" className="form-label">Lösenord</label>
                  <div className="login-input-container">
                    <div className="login-input-icon">
                      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                      </svg>
                    </div>
                    <input
                      id="password"
                      type="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="form-input login-input"
                      placeholder="Ange ditt lösenord"
                      required
                    />
                  </div>
                </div>
              </div>
              
              <div className="login-form-actions">
                <button
                  type="submit"
                  disabled={isLoading}
                  className="action-button primary login-submit-button"
                >
                  {isLoading ? (
                    <>
                      <div className="login-loading-spinner">
                        <svg viewBox="0 0 24 24">
                          <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" opacity="0.25"></circle>
                          <path fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" opacity="0.75"></path>
                        </svg>
                      </div>
                      Loggar in...
                    </>
                  ) : (
                    <>
                      <svg className="login-button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                      </svg>
                      Logga in
                    </>
                  )}
                </button>
                
              </div>
            </form>
            
            <div className="login-footer">
              <div className="login-footer-badges">
                <div className="login-footer-badge">
                  <svg fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 2a1 1 0 011 1v1.323l3.954 1.582 1.599-.8a1 1 0 011.2 1.6l-.8 1.6 1.6.8a1 1 0 01-1.6 1.2l-1.6-.8L13.323 11H15a1 1 0 110 2h-1.323l-1.582 3.954.8 1.599a1 1 0 01-1.6 1.2l-1.6-.8-.8 1.6a1 1 0 01-1.2-1.6l.8-1.6L6.677 15H5a1 1 0 110-2h1.323l1.582-3.954-.8-1.599a1 1 0 011.6-1.2l1.6.8.8-1.6a1 1 0 011.2 1.6l-.8 1.6L13.323 9H15a1 1 0 110-2h-1.323L12.095 3.046l.8-1.599a1 1 0 00-1.6-1.2l-1.6.8-.8-1.6A1 1 0 0010 2z"/>
                    <circle cx="10" cy="10" r="2"/>
                  </svg>
                  <span>RPA</span>
                </div>
                <div className="login-footer-badge">
                  <svg fill="currentColor" viewBox="0 0 20 20">
                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                  </svg>
                  <span>ERP</span>
                </div>
                <div className="login-footer-badge">
                  <svg fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                  </svg>
                  <span>Automatisering</span>
                </div>
              </div>
              <p className="login-copyright">© 2025 Automation Hub • Alla rättigheter förbehållna</p>
            </div>
          </div>
        </div>

        {/* Right side - Branding */}
        <div className="login-branding-section">
          <div className="login-branding-container">
            <div className="login-branding-content">
              <div className="login-branding-header">
                <h1>Automation Hub</h1>
                <p>Kraftfull RPA-plattform för automatisering av affärsprocesser</p>
              </div>
              
              <div className="login-features">
                <div className="login-feature">
                  <div className="login-feature-icon">
                    <svg fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z"/>
                      <path d="M6 8a1 1 0 000 2h8a1 1 0 100-2H6z"/>
                      <path d="M6 12a1 1 0 100 2h4a1 1 0 100-2H6z"/>
                    </svg>
                  </div>
                  <span>Visuell flödesdesigner</span>
                </div>

                <div className="login-feature">
                  <div className="login-feature-icon">
                    <svg fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                      <circle cx="10" cy="7" r="2"/>
                      <path d="M10 9c-1.5 0-3 .5-3 2v1h6v-1c0-1.5-1.5-2-3-2z"/>
                    </svg>
                  </div>
                  <span>AI-assisterad automatisering</span>
                </div>

                <div className="login-feature">
                  <div className="login-feature-icon">
                    <svg fill="currentColor" viewBox="0 0 20 20">
                      <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4z"/>
                      <path d="M3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6z"/>
                      <path d="M14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                      <path d="M6 12h2v2H6v-2z"/>
                      <path d="M15 12h1v2h-1v-2z"/>
                    </svg>
                  </div>
                  <span>Mass deployment</span>
                </div>
              </div>
            </div>
          </div>
          
          <div className="login-branding-decorations">
            <div className="login-decoration login-decoration-1"></div>
            <div className="login-decoration login-decoration-2"></div>
            <div className="login-decoration login-decoration-3"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPrompt;
