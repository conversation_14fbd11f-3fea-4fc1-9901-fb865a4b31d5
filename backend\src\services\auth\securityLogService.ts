import { generateId } from '@rpa-project/shared';

export interface SecurityLog {
  id: string;
  type: SecurityLogType;
  userId?: string;
  apiKeyId?: string;
  ip: string;
  userAgent?: string;
  endpoint: string;
  method: string;
  statusCode: number;
  message: string;
  metadata?: Record<string, any>;
  createdAt: Date;
}

export type SecurityLogType =
  | 'login_success'
  | 'login_failed'
  | 'logout'
  | 'token_refresh'
  | 'api_key_used'
  | 'unauthorized_access'
  | 'rate_limit_exceeded'
  | 'suspicious_activity'
  | 'password_changed'
  | 'user_created'
  | 'user_updated'
  | 'api_key_created'
  | 'api_key_revoked';

export interface CreateSecurityLogRequest {
  type: SecurityLogType;
  userId?: string;
  apiKeyId?: string;
  ip: string;
  userAgent?: string;
  endpoint: string;
  method: string;
  statusCode: number;
  message: string;
  metadata?: Record<string, any>;
}

/**
 * Security Log Service for tracking security events
 */
export class SecurityLogService {
  private logs = new Map<string, SecurityLog>();
  private maxLogs = 10000; // Keep last 10,000 logs in memory

  /**
   * Log a security event
   */
  async log(request: CreateSecurityLogRequest): Promise<SecurityLog> {
    const securityLog: SecurityLog = {
      id: generateId(),
      type: request.type,
      userId: request.userId,
      apiKeyId: request.apiKeyId,
      ip: request.ip,
      userAgent: request.userAgent,
      endpoint: request.endpoint,
      method: request.method,
      statusCode: request.statusCode,
      message: request.message,
      metadata: request.metadata,
      createdAt: new Date(),
    };

    this.logs.set(securityLog.id, securityLog);

    // Clean up old logs if we exceed the limit
    if (this.logs.size > this.maxLogs) {
      this.cleanupOldLogs();
    }

    // Log to console for immediate visibility
    this.logToConsole(securityLog);

    return securityLog;
  }

  /**
   * Get security logs with filtering and pagination
   */
  async getLogs(options: {
    type?: SecurityLogType;
    userId?: string;
    apiKeyId?: string;
    ip?: string;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
    offset?: number;
  } = {}): Promise<{ logs: SecurityLog[]; total: number }> {
    let filteredLogs = Array.from(this.logs.values());

    // Apply filters
    if (options.type) {
      filteredLogs = filteredLogs.filter(log => log.type === options.type);
    }

    if (options.userId) {
      filteredLogs = filteredLogs.filter(log => log.userId === options.userId);
    }

    if (options.apiKeyId) {
      filteredLogs = filteredLogs.filter(log => log.apiKeyId === options.apiKeyId);
    }

    if (options.ip) {
      filteredLogs = filteredLogs.filter(log => log.ip === options.ip);
    }

    if (options.startDate) {
      filteredLogs = filteredLogs.filter(log => log.createdAt >= options.startDate!);
    }

    if (options.endDate) {
      filteredLogs = filteredLogs.filter(log => log.createdAt <= options.endDate!);
    }

    // Sort by creation date (newest first)
    filteredLogs.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

    const total = filteredLogs.length;

    // Apply pagination
    const limit = options.limit || 50;
    const offset = options.offset || 0;
    const paginatedLogs = filteredLogs.slice(offset, offset + limit);

    return {
      logs: paginatedLogs,
      total,
    };
  }

  /**
   * Get security statistics
   */
  async getStatistics(timeRange: 'hour' | 'day' | 'week' | 'month' = 'day'): Promise<{
    totalEvents: number;
    eventsByType: Record<SecurityLogType, number>;
    failedLogins: number;
    successfulLogins: number;
    suspiciousActivity: number;
    topIPs: Array<{ ip: string; count: number }>;
    recentEvents: SecurityLog[];
  }> {
    const now = new Date();
    let startTime: Date;

    switch (timeRange) {
      case 'hour':
        startTime = new Date(now.getTime() - 60 * 60 * 1000);
        break;
      case 'day':
        startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case 'week':
        startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
    }

    const recentLogs = Array.from(this.logs.values())
      .filter(log => log.createdAt >= startTime)
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

    // Count events by type
    const eventsByType: Record<SecurityLogType, number> = {
      login_success: 0,
      login_failed: 0,
      logout: 0,
      token_refresh: 0,
      api_key_used: 0,
      unauthorized_access: 0,
      rate_limit_exceeded: 0,
      suspicious_activity: 0,
      password_changed: 0,
      user_created: 0,
      user_updated: 0,
      api_key_created: 0,
      api_key_revoked: 0,
    };

    recentLogs.forEach(log => {
      eventsByType[log.type]++;
    });

    // Count IPs
    const ipCounts = new Map<string, number>();
    recentLogs.forEach(log => {
      ipCounts.set(log.ip, (ipCounts.get(log.ip) || 0) + 1);
    });

    const topIPs = Array.from(ipCounts.entries())
      .map(([ip, count]) => ({ ip, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    return {
      totalEvents: recentLogs.length,
      eventsByType,
      failedLogins: eventsByType.login_failed,
      successfulLogins: eventsByType.login_success,
      suspiciousActivity: eventsByType.suspicious_activity + eventsByType.unauthorized_access,
      topIPs,
      recentEvents: recentLogs.slice(0, 20),
    };
  }

  /**
   * Detect suspicious activity
   */
  async detectSuspiciousActivity(): Promise<{
    suspiciousIPs: string[];
    repeatedFailedLogins: Array<{ ip: string; attempts: number }>;
    unusualActivity: SecurityLog[];
  }> {
    const last24Hours = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const recentLogs = Array.from(this.logs.values())
      .filter(log => log.createdAt >= last24Hours);

    // Track failed login attempts by IP
    const failedLoginsByIP = new Map<string, number>();
    const suspiciousLogs: SecurityLog[] = [];

    recentLogs.forEach(log => {
      if (log.type === 'login_failed') {
        failedLoginsByIP.set(log.ip, (failedLoginsByIP.get(log.ip) || 0) + 1);
      }

      // Mark as suspicious if multiple failed attempts or unauthorized access
      if (log.type === 'unauthorized_access' || 
          log.type === 'rate_limit_exceeded' ||
          log.type === 'suspicious_activity') {
        suspiciousLogs.push(log);
      }
    });

    // IPs with more than 10 failed login attempts
    const suspiciousIPs = Array.from(failedLoginsByIP.entries())
      .filter(([ip, count]) => count > 10)
      .map(([ip]) => ip);

    const repeatedFailedLogins = Array.from(failedLoginsByIP.entries())
      .filter(([ip, count]) => count > 5)
      .map(([ip, attempts]) => ({ ip, attempts }))
      .sort((a, b) => b.attempts - a.attempts);

    return {
      suspiciousIPs,
      repeatedFailedLogins,
      unusualActivity: suspiciousLogs,
    };
  }

  /**
   * Clean up old logs to prevent memory issues
   */
  private cleanupOldLogs(): void {
    const logs = Array.from(this.logs.entries())
      .sort(([, a], [, b]) => b.createdAt.getTime() - a.createdAt.getTime());

    // Keep only the most recent logs
    const logsToKeep = logs.slice(0, this.maxLogs * 0.8); // Keep 80% of max
    const logsToDelete = logs.slice(this.maxLogs * 0.8);

    // Clear old logs
    logsToDelete.forEach(([id]) => {
      this.logs.delete(id);
    });


  }

  /**
   * Log to console with appropriate formatting
   */
  private logToConsole(log: SecurityLog): void {
    const timestamp = log.createdAt.toISOString();
    const level = this.getLogLevel(log.type);
    const emoji = this.getLogEmoji(log.type);
    
    const message = `${emoji} [${timestamp}] ${level.toUpperCase()} - ${log.type}: ${log.message}`;
    
    switch (level) {
      case 'error':
        console.error(message);
        break;
      case 'warn':
        console.warn(message);
        break;
      case 'info':
        console.info(message);
        break;
      default:
        console.log(message);
    }

    // Log additional details for important events
    if (level === 'error' || level === 'warn') {
      console.log(`   IP: ${log.ip}, Endpoint: ${log.endpoint}, Status: ${log.statusCode}`);
      if (log.metadata) {
        console.log(`   Metadata:`, log.metadata);
      }
    }
  }

  /**
   * Get log level based on event type
   */
  private getLogLevel(type: SecurityLogType): string {
    switch (type) {
      case 'login_failed':
      case 'unauthorized_access':
      case 'suspicious_activity':
        return 'error';
      case 'rate_limit_exceeded':
      case 'api_key_revoked':
        return 'warn';
      case 'login_success':
      case 'logout':
      case 'token_refresh':
      case 'password_changed':
      case 'user_created':
      case 'user_updated':
      case 'api_key_created':
      case 'api_key_used':
        return 'info';
      default:
        return 'debug';
    }
  }

  /**
   * Get emoji for log type
   */
  private getLogEmoji(type: SecurityLogType): string {
    switch (type) {
      case 'login_success': return '✅';
      case 'login_failed': return '❌';
      case 'logout': return '👋';
      case 'token_refresh': return '🔄';
      case 'api_key_used': return '🔑';
      case 'unauthorized_access': return '🚫';
      case 'rate_limit_exceeded': return '⏱️';
      case 'suspicious_activity': return '🚨';
      case 'password_changed': return '🔒';
      case 'user_created': return '👤';
      case 'user_updated': return '✏️';
      case 'api_key_created': return '🗝️';
      case 'api_key_revoked': return '🔐';
      default: return '📝';
    }
  }

  /**
   * Export logs for external analysis
   */
  async exportLogs(format: 'json' | 'csv' = 'json'): Promise<string> {
    const logs = Array.from(this.logs.values())
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

    if (format === 'json') {
      return JSON.stringify(logs, null, 2);
    }

    // CSV format
    const headers = ['id', 'type', 'userId', 'ip', 'endpoint', 'method', 'statusCode', 'message', 'createdAt'];
    const csvRows = [headers.join(',')];

    logs.forEach(log => {
      const row = [
        log.id,
        log.type,
        log.userId || '',
        log.ip,
        log.endpoint,
        log.method,
        log.statusCode.toString(),
        `"${log.message.replace(/"/g, '""')}"`, // Escape quotes
        log.createdAt.toISOString(),
      ];
      csvRows.push(row.join(','));
    });

    return csvRows.join('\n');
  }
}

// Export singleton instance
export const securityLogService = new SecurityLogService();
