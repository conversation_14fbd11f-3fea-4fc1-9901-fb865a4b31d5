import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import { User, Api<PERSON>ey, User<PERSON><PERSON> } from '../../types/permissions';
import { securityConfig } from '../../config/security';

export interface JwtPayload {
  userId: string;
  username: string;
  role: UserRole;
  type: 'user' | 'api';
  iat: number;
  exp: number;
}

export interface TokenPair {
  accessToken: string;
  refreshToken: string;
  expiresAt: Date;
}

export interface RefreshTokenData {
  userId: string;
  type: 'user' | 'api';
  tokenId: string;
  createdAt: Date;
}

/**
 * JWT Service for handling authentication tokens
 */
export class JwtService {
  private refreshTokens = new Map<string, RefreshTokenData>();

  /**
   * Generate access token for user
   */
  generateUserToken(user: User): string {
    const payload: JwtPayload = {
      userId: user.id,
      username: user.username,
      role: user.role,
      type: 'user',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + this.parseExpirationTime(securityConfig.jwt.expiresIn),
    };

    return jwt.sign(payload, securityConfig.jwt.secret, {
      algorithm: 'HS256',
    });
  }

  /**
   * Generate access token for API key
   */
  generateApiKeyToken(apiKey: ApiKey): string {
    const payload: JwtPayload = {
      userId: apiKey.id,
      username: apiKey.name,
      role: apiKey.role,
      type: 'api',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + this.parseExpirationTime(securityConfig.jwt.expiresIn),
    };

    return jwt.sign(payload, securityConfig.jwt.secret, {
      algorithm: 'HS256',
    });
  }

  /**
   * Generate refresh token
   */
  generateRefreshToken(userId: string, type: 'user' | 'api'): string {
    const tokenId = crypto.randomBytes(32).toString('hex');
    const refreshToken = crypto.randomBytes(64).toString('hex');

    // Store refresh token data
    this.refreshTokens.set(refreshToken, {
      userId,
      type,
      tokenId,
      createdAt: new Date(),
    });

    return refreshToken;
  }

  /**
   * Generate token pair (access + refresh)
   */
  generateTokenPair(user: User): TokenPair {
    const accessToken = this.generateUserToken(user);
    const refreshToken = this.generateRefreshToken(user.id, 'user');
    const expiresAt = new Date(Date.now() + this.parseExpirationTime(securityConfig.jwt.expiresIn) * 1000);

    return {
      accessToken,
      refreshToken,
      expiresAt,
    };
  }

  /**
   * Generate API key token pair
   */
  generateApiKeyTokenPair(apiKey: ApiKey): TokenPair {
    const accessToken = this.generateApiKeyToken(apiKey);
    const refreshToken = this.generateRefreshToken(apiKey.id, 'api');
    const expiresAt = new Date(Date.now() + this.parseExpirationTime(securityConfig.jwt.expiresIn) * 1000);

    return {
      accessToken,
      refreshToken,
      expiresAt,
    };
  }

  /**
   * Verify and decode JWT token
   */
  verifyToken(token: string): JwtPayload {
    try {
      const decoded = jwt.verify(token, securityConfig.jwt.secret) as JwtPayload;
      
      // Check if token is expired
      if (decoded.exp && decoded.exp < Math.floor(Date.now() / 1000)) {
        throw new Error('Token expired');
      }

      return decoded;
    } catch (error) {
      if (error instanceof jwt.JsonWebTokenError) {
        throw new Error('Invalid token');
      }
      if (error instanceof jwt.TokenExpiredError) {
        throw new Error('Token expired');
      }
      throw error;
    }
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshAccessToken(refreshToken: string, getUserById: (id: string) => Promise<User | null>): Promise<TokenPair | null> {
    const tokenData = this.refreshTokens.get(refreshToken);
    if (!tokenData) {
      return null;
    }

    // Check if refresh token is expired (7 days)
    const refreshExpirationTime = this.parseExpirationTime(securityConfig.jwt.refreshExpiresIn) * 1000;
    if (Date.now() - tokenData.createdAt.getTime() > refreshExpirationTime) {
      this.refreshTokens.delete(refreshToken);
      return null;
    }

    // Get user data
    const user = await getUserById(tokenData.userId);
    if (!user || !user.isActive) {
      this.refreshTokens.delete(refreshToken);
      return null;
    }

    // Generate new token pair
    const newTokenPair = this.generateTokenPair(user);

    // Remove old refresh token
    this.refreshTokens.delete(refreshToken);

    return newTokenPair;
  }

  /**
   * Revoke refresh token
   */
  revokeRefreshToken(refreshToken: string): boolean {
    return this.refreshTokens.delete(refreshToken);
  }

  /**
   * Revoke all refresh tokens for a user
   */
  revokeAllUserTokens(userId: string): number {
    let revokedCount = 0;
    for (const [token, data] of this.refreshTokens.entries()) {
      if (data.userId === userId) {
        this.refreshTokens.delete(token);
        revokedCount++;
      }
    }
    return revokedCount;
  }

  /**
   * Get active refresh tokens for a user
   */
  getUserRefreshTokens(userId: string): RefreshTokenData[] {
    const userTokens: RefreshTokenData[] = [];
    for (const data of this.refreshTokens.values()) {
      if (data.userId === userId) {
        userTokens.push(data);
      }
    }
    return userTokens;
  }

  /**
   * Clean up expired refresh tokens
   */
  cleanupExpiredTokens(): number {
    const refreshExpirationTime = this.parseExpirationTime(securityConfig.jwt.refreshExpiresIn) * 1000;
    let cleanedCount = 0;

    for (const [token, data] of this.refreshTokens.entries()) {
      if (Date.now() - data.createdAt.getTime() > refreshExpirationTime) {
        this.refreshTokens.delete(token);
        cleanedCount++;
      }
    }

    return cleanedCount;
  }

  /**
   * Parse expiration time string to seconds
   */
  private parseExpirationTime(timeString: string): number {
    const match = timeString.match(/^(\d+)([smhd])$/);
    if (!match) {
      throw new Error(`Invalid expiration time format: ${timeString}`);
    }

    const value = parseInt(match[1]);
    const unit = match[2];

    switch (unit) {
      case 's': return value;
      case 'm': return value * 60;
      case 'h': return value * 60 * 60;
      case 'd': return value * 60 * 60 * 24;
      default: throw new Error(`Invalid time unit: ${unit}`);
    }
  }

  /**
   * Extract token from Authorization header
   */
  extractTokenFromHeader(authHeader: string | undefined): string | null {
    if (!authHeader) {
      return null;
    }

    const parts = authHeader.split(' ');
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      return null;
    }

    return parts[1];
  }

  /**
   * Generate secure random string for API keys
   */
  generateSecureRandomString(length: number): string {
    return crypto.randomBytes(Math.ceil(length / 2)).toString('hex').slice(0, length);
  }

  /**
   * Hash API key for storage
   */
  hashApiKey(apiKey: string): string {
    return crypto.createHash('sha256').update(apiKey).digest('hex');
  }

  /**
   * Verify API key hash
   */
  verifyApiKeyHash(apiKey: string, hash: string): boolean {
    const computedHash = this.hashApiKey(apiKey);
    return crypto.timingSafeEqual(Buffer.from(computedHash), Buffer.from(hash));
  }

  /**
   * Get token statistics
   */
  getTokenStatistics() {
    const now = Date.now();
    const refreshExpirationTime = this.parseExpirationTime(securityConfig.jwt.refreshExpiresIn) * 1000;
    
    let activeTokens = 0;
    let expiredTokens = 0;
    let userTokens = 0;
    let apiTokens = 0;

    for (const data of this.refreshTokens.values()) {
      if (now - data.createdAt.getTime() > refreshExpirationTime) {
        expiredTokens++;
      } else {
        activeTokens++;
      }

      if (data.type === 'user') {
        userTokens++;
      } else {
        apiTokens++;
      }
    }

    return {
      total: this.refreshTokens.size,
      active: activeTokens,
      expired: expiredTokens,
      userTokens,
      apiTokens,
    };
  }
}

// Export singleton instance
export const jwtService = new JwtService();
