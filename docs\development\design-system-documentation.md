# RPA Application Design System Documentation

## Översikt
Detta dokument beskriver designsystemet för RPA-applikationen baserat på analys av befintliga sidor: Dashboard, FlowList, Schedules och ExecutionList.

## Färgschema

### Primära färger
- **Bakgrund**: `#fbf9f8` - Varm, ljus bakgrund
- **Text**: `#1a0f0f` - Mörk, läsbar text
- **Primär accent**: `#fd746c` - Röd/orange primärfärg för knappar och accenter
- **Sekundär accent**: `#f2e8e8` - Ljus accent för hover-effekter
- **Vit**: `#ffffff` - För kort och innehållsområden

### Sekundära färger
- **Grå text**: `#6b7280` - För sekundär text och metadata
- **Gränser**: `#e5e7eb` - <PERSON><PERSON>r tabellgränser och kort
- **Hover-bakgrund**: `#fef7f7` - Ljus hover-effekt
- **Hover-accent**: `#fdeae8` - Sekundär hover-effekt

### Statusfärger
- **Slutförd**: `#e8f5e8` (bakgrund), `#1a0f0f` (text)
- **Körs**: `#fff3e0` (bakgrund), `#1a0f0f` (text)  
- **Schemalagd**: `#e3f2fd` (bakgrund), `#1a0f0f` (text)
- **Misslyckad**: `#ffebee` (bakgrund), `#1a0f0f` (text)

## Typografi

### Teckensnitt
- **Primär**: 'Work Sans', 'Noto Sans', sans-serif
- **Monospace**: Monaco, Consolas, "Courier New", monospace (för kod och ID:n)

### Textstorlekar
- **Sidtitel**: `2rem` (32px), font-weight: 500
- **Sektionsrubrik**: `1.375rem` (22px), font-weight: 500
- **Undertitel**: `0.875rem` (14px), font-weight: normal
- **Brödtext**: `0.875rem` (14px), font-weight: normal
- **Sekundär text**: `0.875rem` (14px), color: `#6b7280`

## Layout-struktur

### Huvudlayout
```css
.design-root {
  display: flex;
  min-height: 100vh;
  flex-direction: column;
  background-color: #fbf9f8;
}
```

### Sidebar (280px bred)
- **Position**: Fast vänster sidebar
- **Bakgrund**: `#fbf9f8`
- **Padding**: `1rem`
- **Box-shadow**: `2px 0 4px rgba(0, 0, 0, 0.05)`
- **Border**: `1px solid #e5d2d1` (höger)

### Huvudinnehåll
- **Margin-left**: `calc(320px + 1rem)` (för att ge plats åt sidebar)
- **Margin-right**: `1.5rem`
- **Max-width**: `960px` (centrerat innehåll)

## Sidstruktur

### Dashboard Header Pattern
```css
.dashboard-header {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 0.75rem;
  padding: 1rem;
}

.dashboard-header-content {
  display: flex;
  min-width: 18rem;
  flex-direction: column;
  gap: 0.75rem;
}
```

### Innehållscontainer
```css
.dashboard-container {
  display: flex;
  flex-direction: column;
  flex: 1;
}
```

## Komponenter

### Statistikkort
```css
.stats-grid {
  display: flex !important;
  flex-wrap: nowrap !important;
  gap: 1rem !important;
  padding: 1rem !important;
  overflow-x: auto !important;
}

.stat-card {
  min-width: 280px !important;
  max-width: 400px !important;
  flex-shrink: 0 !important;
  padding: 1.5rem !important;
  background-color: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 0.75rem !important;
}
```

### Tabeller
```css
.table-container {
  padding: 0 1rem 0.75rem 1rem;
}

.activity-table {
  display: flex;
  overflow: hidden;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
  background-color: white;
}

.table {
  flex: 1;
  border-collapse: collapse;
}

.table th {
  padding: 0.75rem 1rem;
  text-align: left;
  color: #1a0f0f;
  font-size: 0.875rem;
  font-weight: 500 !important;
  background-color: white !important;
}

.table td {
  height: 72px;
  padding: 0.5rem 1rem;
  color: #1a0f0f;
  font-size: 0.875rem;
  border-top: 1px solid #e5e7eb;
}
```

### Knappar

#### Primära knappar
```css
.action-button.primary {
  background-color: #fd746c;
  color: white;
  border-radius: 9999px;
  height: 2.5rem;
  padding: 0 1rem;
  font-size: 0.875rem;
  font-weight: 600;
}

.action-button.primary:hover {
  background-color: #fc5a50;
}
```

#### Sekundära knappar
```css
.action-button.secondary {
  background-color: #fef7f7;
  color: #1a0f0f;
  border: 1px solid #fd746c;
}

.action-button.secondary:hover {
  background-color: #fdeae8;
}
```

#### Små åtgärdsknappar
```css
.action-button-small {
  min-width: 60px;
  max-width: 100px;
  height: 2rem;
  padding: 0 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: 9999px;
}
```

### Statusbadges
```css
.status-button {
  display: flex;
  min-width: 84px;
  max-width: 120px;
  height: 2rem;
  padding: 0 1rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
  border: none;
}
```

### Sökfält
```css
.search-input {
  padding: 0 1rem;
  border: 1px solid #d1d5db;
  border-radius: 9999px;
  font-size: 0.875rem;
  height: 2.5rem;
  width: 300px;
  background-color: #ffffff;
}

.search-input:focus {
  border-color: #fd746c;
}
```

### Empty State
```css
.empty-state-container {
  padding: 1rem;
}

.empty-state {
  text-align: center;
  padding: 3rem 1.5rem;
  background-color: white;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
}

.empty-state-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}
```

### Felmeddelanden
```css
.error-card {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.error-title {
  color: #dc2626;
  font-size: 1.125rem;
  font-weight: 600;
}
```

## Designprinciper

### 1. Konsekvent spacing
- **Små gap**: `0.5rem` (8px)
- **Medium gap**: `0.75rem` (12px)  
- **Stora gap**: `1rem` (16px)
- **Sektionsavstånd**: `1.5rem` (24px)

### 2. Border-radius
- **Små element**: `0.375rem` (6px)
- **Kort och knappar**: `0.75rem` (12px)
- **Rundade knappar**: `9999px` (helt rundade)

### 3. Skuggor
- **Subtila skuggor**: `0 1px 3px rgba(0, 0, 0, 0.1)`
- **Inga tunga skuggor** - designen förlitar sig på gränser och färger

### 4. Hover-effekter
- **Smooth transitions**: `transition: all 0.2s`
- **Subtila färgförändringar** istället för dramatiska effekter
- **Transform-effekter**: `translateX(2px)` för länkar

### 5. Responsivitet
- **Flexbox-baserad layout**
- **Min/max-width constraints** på kort
- **Overflow-x: auto** för horisontell scrollning på mobil

## Navigationsdesign

### Sidebar Navigation
- **Ikoner + text** för alla navigationsobjekt
- **Aktiv state**: `background-color: #fef7f7`, `border-color: #fd746c`
- **Hover state**: `background-color: #fef7f7`
- **Rundade navigationsobjekt**: `border-radius: 9999px`

### Breadcrumbs/Tillbaka-knappar
- Används i flow editor och formulär
- Placeras i övre vänstra hörnet

## Formulärdesign

### Inputfält
```css
.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  background-color: #ffffff;
}

.form-input:focus {
  border-color: #fd746c;
  box-shadow: 0 0 0 3px rgba(253, 116, 108, 0.1);
}
```

### Labels
```css
.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: #1a0f0f;
  margin-bottom: 0.5rem;
}
```

## Användningsriktlinjer

### Sidstruktur
1. **Header** med titel, undertitel och åtgärdsknappar
2. **Statistikkort** (om tillämpligt) i horisontell rad
3. **Sektionsrubrik** för huvudinnehåll
4. **Tabellcontainer** eller innehållsområde
5. **Empty states** för tomma listor

### Knappplacering
- **Primära åtgärder**: Höger sida av header
- **Tabellåtgärder**: Höger kolumn med ikoner
- **Formuläråtgärder**: Botten av formulär

### Färganvändning
- **Sparsam användning** av primärfärgen (#fd746c)
- **Vit bakgrund** för innehållsområden
- **Grå text** för metadata och sekundär information
- **Statusfärger** endast för status-indikatorer

### Typografi
- **Konsekvent hierarki** med definierade storlekar
- **Medium font-weight** för rubriker (500)
- **Normal font-weight** för brödtext (400)
- **Semibold** för knappar och viktiga element (600)

## Interaktionsmönster

### Klickbara element
- **Första kolumnen i tabeller**: Klickbar för navigation med `cursor: pointer`
- **Flödeslänkar**: Hover-effekt med `transform: translateX(2px)` och färgändring
- **Navigationsobjekt**: Smooth transitions med färg- och bakgrundsförändringar

### Loading states
```css
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 16rem;
}

.loading-text {
  color: #6b7280;
  font-size: 1.125rem;
}
```

### Hover-effekter
- **Tabellrader**: Subtil bakgrundsförändring till `#f9fafb`
- **Knappar**: Mörkare nyans av grundfärgen
- **Länkar**: Färgändring och understreck

### Fokushantering
- **Inputfält**: Border-färg ändras till primärfärg med box-shadow
- **Knappar**: Outline för tillgänglighet
- **Navigering**: Tydlig visuell feedback

## Specifika komponenter

### FlowList-specifika element
```css
.flow-name {
  font-weight: 500;
  color: #1a0f0f;
  margin-bottom: 0.25rem;
}

.flow-description-small {
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.4;
}

.flow-link {
  display: block;
  text-decoration: none;
  color: inherit;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 0.25rem 0;
  border-radius: 0.375rem;
}

.flow-link:hover {
  background-color: #f8fafc;
  transform: translateX(2px);
}
```

### Sökfunktionalitet
- **Sökfält**: 300px bred med rundade hörn
- **Placeholder**: Inkluderar emoji för visuell guidning (🔎)
- **Clear-knapp**: Visas när text finns, positionerad absolut till höger
- **Sökresultat**: Visar antal träffar i sektionsrubriken

### Infinite scroll (FlowList)
- **Intersection Observer** för automatisk laddning
- **Loading indicator**: Centrerad text under tabellen
- **End indicator**: "Alla flöden har laddats" meddelande

### Auto-refresh (ExecutionList)
- **5-sekunders intervall** för automatisk uppdatering
- **Refresh-knapp**: Manuell uppdatering med disabled state under laddning
- **Loading state**: Behåller befintlig data under uppdatering

## Responsiv design

### Breakpoints
- **Mobile**: < 640px - Single column layout
- **Tablet**: 640px - 1024px - Adjusted grid columns
- **Desktop**: > 1024px - Full layout med sidebar

### Anpassningar
- **Stats-kort**: Horisontell scrollning på mindre skärmar
- **Tabeller**: Behåller struktur men kan scrolla horisontellt
- **Sidebar**: Döljs på mobil (implementeras senare)
- **Knappar**: Behåller storlek men kan stackas vertikalt

## Tillgänglighet

### Färgkontrast
- **Text på bakgrund**: Hög kontrast mellan #1a0f0f och #fbf9f8
- **Statusfärger**: Tillräcklig kontrast för läsbarhet
- **Hover states**: Tydlig visuell feedback

### Semantisk HTML
- **Tabeller**: Korrekt thead/tbody struktur
- **Knappar**: Button-element för interaktioner
- **Länkar**: Anchor-element för navigation
- **Rubriker**: Hierarkisk h1-h6 struktur

### Keyboard navigation
- **Tab-ordning**: Logisk navigation genom sidan
- **Focus indicators**: Synliga focus states
- **Skip links**: För huvudinnehåll (kan implementeras)

## Prestanda

### CSS-optimering
- **Återanvändning**: Gemensamma klasser för liknande element
- **Specificity**: Låg specificity för enkel override
- **Transitions**: Begränsade till transform och opacity för bästa prestanda

### Bildoptimering
- **SVG-ikoner**: Vektorbaserade för skärphet
- **Inline SVG**: För bättre kontroll och styling
- **Icon system**: Konsekvent storlek (24x24px)

## Framtida utveckling

### Mörkt tema
- **CSS custom properties**: Förberedd struktur för tema-växling
- **Färgvariabler**: Centraliserade färgdefinitioner
- **Komponentanpassning**: Alla komponenter använder systemfärger

### Animationer
- **Micro-interactions**: Subtila animationer för feedback
- **Page transitions**: Smooth övergångar mellan sidor
- **Loading states**: Skeleton screens för bättre UX

### Modulär CSS
- **Komponentbaserad**: Varje komponent har egen CSS-sektion
- **Utility classes**: Gemensamma hjälpklasser
- **Design tokens**: Centraliserade designvärden

## Modal System

### Modal-struktur
Modal-systemet använder React Portal för att rendera modaler utanför den normala DOM-hierarkin, direkt i `document.body`. Detta säkerställer korrekt z-index hantering och förhindrar CSS-konflikter.

### Portal-komponent
```typescript
// Portal.tsx - Renderar children i document.body
export function Portal({ children }: PortalProps) {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
    return () => setMounted(false)
  }, [])

  if (!mounted) return null
  return createPortal(children, document.body)
}
```

### Modal CSS-klasser
```css
.modal-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 1000 !important;
  padding: 1rem !important;
  backdrop-filter: blur(2px);
}

.modal-content {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  max-height: 90vh;
  width: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  margin: 0;
  animation: modalSlideIn 0.2s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
```

### Modal-storlekar
- **Standard bredd**: `maxWidth: '70vw'` (70% av viewport-bredden)
- **Responsiv**: Anpassar sig automatiskt till skärmstorlek
- **Max-höjd**: `90vh` för att säkerställa att modalen inte går utanför skärmen

### Modal-struktur (HTML)
```html
<Portal>
  <div className="modal-overlay" onClick={onCancel}>
    <div
      className="modal-content"
      style={{ width: '100%', maxWidth: '70vw' }}
      onClick={(e) => e.stopPropagation()}
    >
      <!-- Modal Header -->
      <div style={{
        flexShrink: 0,
        borderBottom: '1px solid #e5e7eb',
        padding: '1.5rem'
      }}>
        <p className="dashboard-title" style={{ fontSize: '1.5rem', margin: 0 }}>
          Modal Titel
        </p>
      </div>

      <!-- Modal Body -->
      <div style={{
        flex: 1,
        overflow: 'auto',
        padding: '1.5rem'
      }}>
        <!-- Innehåll här -->
      </div>

      <!-- Modal Footer -->
      <div style={{
        flexShrink: 0,
        borderTop: '1px solid #e5e7eb',
        padding: '1.5rem',
        display: 'flex',
        justifyContent: 'flex-end',
        gap: '0.75rem'
      }}>
        <!-- Knappar här -->
      </div>
    </div>
  </div>
</Portal>
```

### Modal-interaktioner

#### Stängning
- **Overlay-klick**: Klick utanför modal-innehållet stänger modalen
- **Event propagation**: `e.stopPropagation()` på modal-innehåll förhindrar stängning
- **Escape-tangent**: Kan implementeras via `useEffect` och `keydown`-lyssnare

#### Fokushantering
- **Auto-fokus**: Första input-fält får fokus när modal öppnas
- **Fokus-fälla**: Fokus stannar inom modalen (kan implementeras)
- **Återställning**: Fokus återgår till utlösande element när modal stängs

### Modal-formulär

#### Formulärstruktur
```css
form {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: #1a0f0f;
  margin-bottom: 0.5rem;
}

.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  background-color: #ffffff;
}

.form-input:focus {
  border-color: #fd746c;
  box-shadow: 0 0 0 3px rgba(253, 116, 108, 0.1);
}
```

#### Hjälptext
```css
/* Hjälptext under input-fält */
.form-help-text {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
}
```

#### Validering
- **Obligatoriska fält**: Markeras med `*` i label
- **Realtidsvalidering**: Knappar inaktiveras när obligatoriska fält är tomma
- **Felmeddelanden**: Visas med `error-card` klassen

### Modal-knappar

#### Knappplacering
- **Höger-justerade**: Knappar placeras till höger i footer
- **Gap**: `0.75rem` mellan knappar
- **Ordning**: Avbryt (vänster), Primär åtgärd (höger)

#### Knappstatus
```css
/* Inaktiverad knapp */
.action-button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

/* Loading state */
.action-button.loading {
  cursor: not-allowed;
  opacity: 0.7;
}
```

### Specialkomponenter i modaler

#### Informationsruta
```css
.info-box {
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-top: 1rem;
}

.info-box-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #374151;
}

.info-box-content {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
}
```

#### Lösenordsfält
- **Type**: `password` för säkra fält (API tokens, refresh tokens)
- **Placeholder**: Olika text för skapa vs redigera läge
- **Hjälptext**: Förklarar beteende vid redigering ("Lämna tomt för att behålla nuvarande")

### Modal-användningsriktlinjer

#### När att använda modaler
- **Formulär**: Skapa/redigera entiteter
- **Bekräftelser**: Viktiga åtgärder som kräver bekräftelse
- **Detaljvyer**: Visa information utan att lämna nuvarande kontext

#### Storlek och layout
- **Maxbredd**: 70% av viewport för bra läsbarhet
- **Responsiv**: Anpassar sig till mindre skärmar
- **Scrollning**: Endast modal-body scrollar, header och footer är fasta

#### Innehållsriktlinjer
- **Tydlig titel**: Beskriver vad modalen gör
- **Logisk gruppering**: Relaterade fält grupperas tillsammans
- **Hjälptext**: Förklarar komplexa fält eller beteenden
- **Felhantering**: Tydliga felmeddelanden med förslag på lösning

#### Tillgänglighet
- **ARIA-labels**: För skärmläsare
- **Fokushantering**: Logisk tab-ordning
- **Escape-hantering**: Stäng med Escape-tangent
- **Kontrast**: Tillräcklig kontrast för alla textelement

## Step Editor Design System

### Grid-baserad layout
Alla step editors ska använda en konsekvent grid-baserad layout för optimal användning av tillgängligt utrymme.

#### Grundstruktur
```typescript
// Huvudcontainer - EN grid för hela step editorn
<div style={{
  display: 'grid',
  gridTemplateColumns: '1fr 1fr',  // Exakt 2 kolumner
  gap: '1rem',                     // Konsekvent gap
  width: '100%'                    // Full bredd
}}>
```

#### Komponentplacering
Komponenter placeras enligt följande regler:

**1 kolumn (standard):**
- Namn
- Beskrivning
- Variabelnamn
- Timeout
- Mindre knappar (Testa Script)
- Mindre resultatområden (Testresultat)

**2 kolumner (full bredd):**
- Kodeditorer (Monaco Editor)
- Stora textområden
- Tabeller
- Stora formulärområden

#### Implementation
```typescript
{/* 1 kolumn - standard */}
<div className="form-group">
  <label className="form-label">Fältnamn</label>
  <input className="form-input" />
</div>

{/* 2 kolumner - full bredd */}
<div style={{ gridColumn: '1 / -1' }} className="form-group">
  <label className="form-label">Stort innehåll</label>
  <Editor height="400px" />
</div>
```

### Modal-storlek för step editors
Step editors använder större modaler än standardmodaler för att ge plats åt kodeditorer och komplexa formulär.

```css
.step-editor-modal {
  max-width: 90vw;  /* 90% av viewport-bredd */
  max-height: 90vh; /* 90% av viewport-höjd */
}
```

### Formulärkomponenter

#### Labels
```css
.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: #1a0f0f;
  margin-bottom: 0.5rem;
}
```

#### Input-fält
```css
.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  background-color: #ffffff;
}

.form-input:focus {
  border-color: #fd746c;
  box-shadow: 0 0 0 3px rgba(253, 116, 108, 0.1);
}
```

#### Hjälptext
```css
.form-help-text {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
}
```

### Kodeditor-integration

#### Monaco Editor styling
```typescript
<Editor
  height="400px"
  defaultLanguage="javascript"
  theme="vs-light"
  options={{
    minimap: { enabled: false },
    scrollBeyondLastLine: false,
    fontSize: 14,
    lineNumbers: 'on',
    roundedSelection: false,
    automaticLayout: true,
    wordWrap: 'on',
    tabSize: 2,
    insertSpaces: true,
    padding: { top: 16, bottom: 16 }
  }}
/>
```

#### Editor-container
```css
.editor-container {
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  overflow: hidden;
  background-color: white;
}
```

#### Hjälptext för kodeditorer
```css
.code-help-box {
  margin-top: 0.75rem;
  padding: 0.75rem;
  background-color: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  color: #6b7280;
}

.code-help-section {
  margin-bottom: 0.5rem;
}

.code-help-section:last-child {
  margin-bottom: 0;
}

.code-help-strong {
  color: #1a0f0f;
  font-weight: 500;
}

.code-help-code {
  background-color: #e5e7eb;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.8rem;
  font-family: monospace;
}
```

### Designprinciper för step editors

#### 1. Konsekvent grid-användning
- **Alla step editors** ska använda samma grid-struktur
- **2 kolumner max** - aldrig fler kolumner
- **1fr 1fr** - kolumner ska alltid ha samma bredd
- **Full bredd** - grid-containern ska alltid vara 100% bred

#### 2. Komponentstorlek
- **Små komponenter** (input, select, button) = 1 kolumn
- **Stora komponenter** (editor, textarea, table) = 2 kolumner
- **Logisk gruppering** - relaterade fält på samma rad

#### 3. Spacing och layout
- **Gap**: `1rem` mellan grid-kolumner
- **Margin**: `1rem` mellan form-groups
- **Padding**: `0.75rem 1rem` för input-fält

#### 4. Responsivitet
- **Grid-systemet** anpassar sig automatiskt
- **Komponenter** behåller sina proportioner
- **Modal-storlek** anpassas till skärmstorlek

### Exempel på korrekt implementation

#### ScriptStepEditor struktur
```typescript
<StepEditorLayout>
  <div style={{
    display: 'grid',
    gridTemplateColumns: '1fr 1fr',
    gap: '1rem',
    width: '100%'
  }}>
    {/* Namn - 1 kolumn */}
    <div className="form-group">
      <label className="form-label">Namn</label>
      <input className="form-input" />
    </div>

    {/* Beskrivning - 1 kolumn */}
    <div className="form-group">
      <label className="form-label">Beskrivning</label>
      <textarea className="form-input form-textarea" />
    </div>

    {/* Kodeditor - 2 kolumner */}
    <div style={{ gridColumn: '1 / -1' }} className="form-group">
      <label className="form-label">JavaScript-kod</label>
      <div className="editor-container">
        <Editor />
      </div>
      <div className="code-help-box">
        {/* Hjälptext */}
      </div>
    </div>

    {/* Testa Script - 1 kolumn */}
    <div className="form-group">
      <button className="action-button secondary">
        Testa Script
      </button>
    </div>

    {/* Testresultat - 1 kolumn */}
    <div className="form-group">
      <label className="form-label">Testresultat</label>
      <pre className="test-result">
        {/* Resultat */}
      </pre>
    </div>

    {/* Variabelnamn - 1 kolumn */}
    <div className="form-group">
      <label className="form-label">Variabelnamn</label>
      <input className="form-input" />
    </div>

    {/* Timeout - 1 kolumn */}
    <div className="form-group">
      <label className="form-label">Timeout</label>
      <input className="form-input" type="number" />
    </div>
  </div>
</StepEditorLayout>
```

### Riktlinjer för nya step editors

#### 1. Använd alltid grid-systemet
- Starta med grid-container som första element
- Placera alla komponenter inom griden
- Använd `gridColumn: '1 / -1'` för komponenter som behöver full bredd

#### 2. Följ komponentstorlek-reglerna
- Små fält = 1 kolumn
- Stora komponenter = 2 kolumner
- Gruppera relaterade fält på samma rad

#### 3. Konsekvent styling
- Använd `form-label` för alla labels
- Använd `form-input` för alla input-fält
- Använd `form-group` för alla fält-containers

#### 4. Testning
- Testa i olika skärmstorlekar
- Verifiera att alla komponenter använder full tillgänglig bredd
- Kontrollera att grid-layouten fungerar korrekt
