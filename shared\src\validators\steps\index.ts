import { RpaStep } from '../../types';
import { ValidationResult, ValidationError } from '../../utils';
import { validateNavigationStep } from './navigation';
import { validateInteractionStep } from './interaction';
import { validateWaitingStep } from './waiting';
import { validateExtractionStep } from './extraction';
import { validateCredentialStep } from './credentials';
import { validateFileStep } from './files';
import { validateConditionalStep } from './conditional';
import { validateAiStep } from './ai';
import { validateApiStep } from './api';

export * from './navigation';
export * from './interaction';
export * from './waiting';
export * from './extraction';
export * from './credentials';
export * from './files';
export * from './conditional';
export * from './ai';
export * from './api';

export function validateStep(step: RpaStep): ValidationResult {
  const errors: ValidationError[] = [];

  // Validate common fields
  if (!step.id || step.id.trim() === '') {
    errors.push({
      field: 'id',
      message: 'Step ID is required',
      
    });
  }

  if (!step.type || step.type.trim() === '') {
    errors.push({
      field: 'type',
      message: 'Step type is required',
      
    });
  }

  if (step.timeout && (step.timeout < 0 || step.timeout > 300000)) {
    errors.push({
      field: 'timeout',
      message: 'Timeout must be between 0 and 300000ms',
      
    });
  }

  // Validate type-specific fields using category validators
  let categoryValidation: ValidationResult | null = null;

  switch ((step as any).type) {
    case 'navigate':
    case 'goBack':
    case 'goForward':
    case 'reload':
      categoryValidation = validateNavigationStep(step);
      break;

    case 'click':
    case 'fill':
    case 'type':
    case 'selectOption':
    case 'check':
    case 'uncheck':
      categoryValidation = validateInteractionStep(step);
      break;

    case 'waitForSelector':
    case 'waitForTimeout':
    case 'waitForUrl':
      categoryValidation = validateWaitingStep(step);
      break;

    case 'extractText':
    case 'extractAttribute':
    case 'takeScreenshot':
      categoryValidation = validateExtractionStep(step);
      break;

    case 'fillPassword':
    case 'fill2FA':
      categoryValidation = validateCredentialStep(step);
      break;

    case 'downloadFile':
      categoryValidation = validateFileStep(step);
      break;

    case 'ifElementExists':
    case 'conditionalClick':
      categoryValidation = validateConditionalStep(step);
      break;

    case 'extractPdfValues':
    case 'script':
      categoryValidation = validateAiStep(step);
      break;

    case 'fortnoxCreateVoucher':
    case 'fortnoxUploadFile':
    case 'fortnoxAttachFileToVoucher':
    case 'fortnoxUploadAndCreateVoucher':
    case 'eAccountingUploadFile':
    case 'eAccountingCreateVoucher':
    case 'eAccountingAttachFileToVoucher':
    case 'eAccountingUploadAndCreateVoucher':
      categoryValidation = validateApiStep(step);
      break;

    default:
      errors.push({
        field: 'type',
        message: `Unknown step type: ${(step as any).type}`,
        
      });
  }

  // Merge category validation errors
  if (categoryValidation && !categoryValidation.isValid) {
    errors.push(...categoryValidation.errors);
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

export function validateStepType(type: string): boolean {
  const validTypes = [
    'navigate', 'goBack', 'goForward', 'reload',
    'click', 'fill', 'type', 'selectOption', 'check', 'uncheck',
    'waitForSelector', 'waitForTimeout', 'waitForUrl',
    'extractText', 'extractAttribute', 'takeScreenshot',
    'ifElementExists', 'conditionalClick',
    'fillPassword', 'fill2FA', 'downloadFile', 'extractPdfValues', 'script',
    'fortnoxCreateVoucher', 'fortnoxUploadFile', 'fortnoxAttachFileToVoucher', 'fortnoxUploadAndCreateVoucher',
    'eAccountingUploadFile', 'eAccountingCreateVoucher', 'eAccountingAttachFileToVoucher', 'eAccountingUploadAndCreateVoucher'
  ];

  return validTypes.includes(type);
}


