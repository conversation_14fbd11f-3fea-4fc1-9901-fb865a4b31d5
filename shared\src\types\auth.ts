/**
 * Authentication and Authorization Types
 */

export interface User {
  id: string;
  username: string;
  email: string;
  role: UserRole;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  lastLoginAt?: Date;
}

export type UserRole = 'admin' | 'operator' | 'viewer' | 'api';

export interface CreateUserRequest {
  username: string;
  email: string;
  password: string;
  role: UserRole;
}

export interface UpdateUserRequest {
  username?: string;
  email?: string;
  role?: UserRole;
  isActive?: boolean;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  user: Omit<User, 'password'>;
  token: string;
  refreshToken: string;
  expiresAt: Date;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

export interface ApiKey {
  id: string;
  name: string;
  description?: string;
  keyHash: string;
  role: UserRole;
  isActive: boolean;
  expiresAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  lastUsedAt?: Date;
  usageCount: number;
}

export interface CreateApiKeyRequest {
  name: string;
  description?: string;
  role: UserRole;
  expiresAt?: Date;
}

export interface UpdateApiKeyRequest {
  name?: string;
  description?: string;
  role?: UserRole;
  isActive?: boolean;
  expiresAt?: Date;
}

export interface ApiKeyResponse {
  apiKey: Omit<ApiKey, 'keyHash'>;
  key: string; // Only returned on creation
}

export interface JwtPayload {
  userId: string;
  username: string;
  role: UserRole;
  type: 'user' | 'api';
  iat: number;
  exp: number;
}

export interface AuthContext {
  user?: User;
  apiKey?: ApiKey;
  role: UserRole;
  type: 'user' | 'api';
}

export interface SecurityLog {
  id: string;
  type: SecurityLogType;
  userId?: string;
  apiKeyId?: string;
  ip: string;
  userAgent?: string;
  endpoint: string;
  method: string;
  statusCode: number;
  message: string;
  metadata?: Record<string, any>;
  createdAt: Date;
}

export type SecurityLogType = 
  | 'login_success'
  | 'login_failed'
  | 'logout'
  | 'token_refresh'
  | 'api_key_used'
  | 'unauthorized_access'
  | 'rate_limit_exceeded'
  | 'suspicious_activity'
  | 'password_changed'
  | 'user_created'
  | 'user_updated'
  | 'api_key_created'
  | 'api_key_revoked';

export interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Max requests per window
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

export interface SecurityConfig {
  jwt: {
    secret: string;
    expiresIn: string;
    refreshExpiresIn: string;
  };
  rateLimits: {
    global: RateLimitConfig;
    auth: RateLimitConfig;
    api: RateLimitConfig;
  };
  security: {
    bcryptRounds: number;
    maxLoginAttempts: number;
    lockoutDuration: number; // in minutes
    sessionTimeout: number; // in minutes
    requireHttps: boolean;
    allowedOrigins: string[];
    ipWhitelist?: string[];
  };
}

// Permission definitions
export interface Permission {
  resource: string;
  action: string;
}

export const PERMISSIONS = {
  // Flow permissions
  FLOWS_READ: { resource: 'flows', action: 'read' },
  FLOWS_CREATE: { resource: 'flows', action: 'create' },
  FLOWS_UPDATE: { resource: 'flows', action: 'update' },
  FLOWS_DELETE: { resource: 'flows', action: 'delete' },
  FLOWS_EXECUTE: { resource: 'flows', action: 'execute' },

  // Execution permissions
  EXECUTIONS_READ: { resource: 'executions', action: 'read' },
  EXECUTIONS_CANCEL: { resource: 'executions', action: 'cancel' },
  EXECUTIONS_RETRY: { resource: 'executions', action: 'retry' },

  // Schedule permissions
  SCHEDULES_READ: { resource: 'schedules', action: 'read' },
  SCHEDULES_CREATE: { resource: 'schedules', action: 'create' },
  SCHEDULES_UPDATE: { resource: 'schedules', action: 'update' },
  SCHEDULES_DELETE: { resource: 'schedules', action: 'delete' },

  // Customer permissions
  CUSTOMERS_READ: { resource: 'customers', action: 'read' },
  CUSTOMERS_CREATE: { resource: 'customers', action: 'create' },
  CUSTOMERS_UPDATE: { resource: 'customers', action: 'update' },
  CUSTOMERS_DELETE: { resource: 'customers', action: 'delete' },

  // Credential permissions
  CREDENTIALS_READ: { resource: 'credentials', action: 'read' },
  CREDENTIALS_CREATE: { resource: 'credentials', action: 'create' },
  CREDENTIALS_UPDATE: { resource: 'credentials', action: 'update' },
  CREDENTIALS_DELETE: { resource: 'credentials', action: 'delete' },

  // Settings permissions
  SETTINGS_READ: { resource: 'settings', action: 'read' },
  SETTINGS_UPDATE: { resource: 'settings', action: 'update' },

  // User management permissions
  USERS_READ: { resource: 'users', action: 'read' },
  USERS_CREATE: { resource: 'users', action: 'create' },
  USERS_UPDATE: { resource: 'users', action: 'update' },
  USERS_DELETE: { resource: 'users', action: 'delete' },

  // API key permissions
  API_KEYS_READ: { resource: 'api_keys', action: 'read' },
  API_KEYS_CREATE: { resource: 'api_keys', action: 'create' },
  API_KEYS_UPDATE: { resource: 'api_keys', action: 'update' },
  API_KEYS_DELETE: { resource: 'api_keys', action: 'delete' },

  // Security permissions
  SECURITY_LOGS_READ: { resource: 'security_logs', action: 'read' },
} as const;

// Role-based permissions mapping
export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  admin: Object.values(PERMISSIONS),
  operator: [
    PERMISSIONS.FLOWS_READ,
    PERMISSIONS.FLOWS_CREATE,
    PERMISSIONS.FLOWS_UPDATE,
    PERMISSIONS.FLOWS_EXECUTE,
    PERMISSIONS.EXECUTIONS_READ,
    PERMISSIONS.EXECUTIONS_CANCEL,
    PERMISSIONS.EXECUTIONS_RETRY,
    PERMISSIONS.SCHEDULES_READ,
    PERMISSIONS.SCHEDULES_CREATE,
    PERMISSIONS.SCHEDULES_UPDATE,
    PERMISSIONS.CUSTOMERS_READ,
    PERMISSIONS.CREDENTIALS_READ,
    PERMISSIONS.CREDENTIALS_CREATE,
    PERMISSIONS.CREDENTIALS_UPDATE,
    PERMISSIONS.SETTINGS_READ,
  ],
  viewer: [
    PERMISSIONS.FLOWS_READ,
    PERMISSIONS.EXECUTIONS_READ,
    PERMISSIONS.SCHEDULES_READ,
    PERMISSIONS.CUSTOMERS_READ,
    PERMISSIONS.CREDENTIALS_READ,
    PERMISSIONS.SETTINGS_READ,
  ],
  api: [
    PERMISSIONS.FLOWS_READ,
    PERMISSIONS.FLOWS_EXECUTE,
    PERMISSIONS.EXECUTIONS_READ,
    PERMISSIONS.CUSTOMERS_READ,
  ],
};

export function hasPermission(role: UserRole, permission: Permission): boolean {
  const rolePermissions = ROLE_PERMISSIONS[role];
  return rolePermissions.some(
    p => p.resource === permission.resource && p.action === permission.action
  );
}
