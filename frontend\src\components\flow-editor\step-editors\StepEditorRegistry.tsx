import { ComponentType } from 'react'
import { RpaStep } from '@rpa-project/shared'
import { BaseStepEditorProps } from './base'

// Import all step editors
import { NavigationStepEditor } from './navigation'
import { InteractionStepEditor } from './interaction'
import { WaitingStepEditor } from './waiting'
import { CredentialsStepEditor } from './credentials'
import { ExtractionStepEditor } from './extraction'
import { AIStepEditor } from './ai'
import { APIStepEditor } from './api'

// Step category mappings
export const STEP_CATEGORY_MAPPING = {
  // Navigation
  navigate: 'navigation',
  goBack: 'navigation',
  goForward: 'navigation',
  reload: 'navigation',
  
  // Interaction
  click: 'interaction',
  fill: 'interaction',
  type: 'interaction',
  selectOption: 'interaction',
  check: 'interaction',
  uncheck: 'interaction',
  conditionalClick: 'interaction',
  ifElementExists: 'interaction',
  
  // Waiting
  waitForSelector: 'waiting',
  waitForTimeout: 'waiting',
  waitForUrl: 'waiting',
  
  // Credentials
  fillPassword: 'credentials',
  fill2FA: 'credentials',
  
  // Extraction
  extractText: 'extraction',
  extractAttribute: 'extraction',
  takeScreenshot: 'extraction',
  downloadFile: 'extraction',
  
  // AI
  extractPdfValues: 'ai',
  script: 'ai',

  // API
  apiCall: 'api',
  apiAuth: 'api',
  fortnoxCreateVoucher: 'api',
  fortnoxUploadFile: 'api',
  fortnoxAttachFileToVoucher: 'api',
  fortnoxUploadAndCreateVoucher: 'api',
  eAccountingUploadFile: 'api',
  eAccountingCreateVoucher: 'api',
  eAccountingAttachFileToVoucher: 'api',
  eAccountingUploadAndCreateVoucher: 'api'
} as const

// Editor component mappings - using any to avoid complex type issues
export const STEP_EDITOR_MAPPING: Record<string, ComponentType<any>> = {
  navigation: NavigationStepEditor,
  interaction: InteractionStepEditor,
  waiting: WaitingStepEditor,
  credentials: CredentialsStepEditor,
  extraction: ExtractionStepEditor,
  ai: AIStepEditor,
  api: APIStepEditor
}

/**
 * Get the appropriate step editor component for a given step type
 */
export function getStepEditor(stepType: string): ComponentType<any> {
  const category = STEP_CATEGORY_MAPPING[stepType as keyof typeof STEP_CATEGORY_MAPPING]

  if (!category) {
    console.warn(`No category found for step type: ${stepType}`)
    return APIStepEditor // Fallback to API editor which shows "coming soon"
  }

  const EditorComponent = STEP_EDITOR_MAPPING[category]

  if (!EditorComponent) {
    console.warn(`No editor component found for category: ${category}`)
    return APIStepEditor // Fallback to API editor
  }

  return EditorComponent
}

/**
 * Get the category for a given step type
 */
export function getStepCategory(stepType: string): string {
  return STEP_CATEGORY_MAPPING[stepType as keyof typeof STEP_CATEGORY_MAPPING] || 'api'
}

/**
 * Check if a step type is supported
 */
export function isStepTypeSupported(stepType: string): boolean {
  return stepType in STEP_CATEGORY_MAPPING
}

/**
 * Get all supported step types for a category
 */
export function getStepTypesForCategory(category: string): string[] {
  return Object.entries(STEP_CATEGORY_MAPPING)
    .filter(([_, cat]) => cat === category)
    .map(([stepType, _]) => stepType)
}

/**
 * Get all available categories
 */
export function getAllCategories(): string[] {
  return Array.from(new Set(Object.values(STEP_CATEGORY_MAPPING)))
}

/**
 * Universal step editor component that automatically selects the right editor
 */
interface UniversalStepEditorProps extends BaseStepEditorProps {
  step: RpaStep
}

export function UniversalStepEditor(props: UniversalStepEditorProps) {
  const EditorComponent = getStepEditor(props.step.type)
  return <EditorComponent {...props} />
}
