// Test script för LLM provider-arkitekturen
// Load environment variables from .env file
require('dotenv').config();

const { LLMService } = require('../dist/services/llm/LLMService');
const { LLMProviderFactory } = require('../dist/services/llm/LLMProviderFactory');

async function testLLMService() {
  console.log('🧪 Testing LLM Service...');

  try {
    // Test 1: OpenAI Provider
    console.log('\n1. Testing OpenAI provider...');
    process.env.LLM_PROVIDER = 'openai';
    LLMProviderFactory.reset();
    const openaiHealthCheck = await LLMService.testConnection();
    console.log('OpenAI health check result:', openaiHealthCheck);

    if (openaiHealthCheck.success) {
      console.log('\n2. Testing OpenAI chat completion...');
      const openaiResponse = await LLMService.createChatCompletion([
        { role: 'user', content: 'Säg hej på svenska' }
      ], {
        maxTokens: 50
      });
      console.log('OpenAI Response:', openaiResponse.content);
      console.log('OpenAI Usage:', openaiResponse.usage);
    }

    // Test 2: Azure Provider
    console.log('\n3. Testing Azure provider...');
    process.env.LLM_PROVIDER = 'azure';
    LLMProviderFactory.reset();
    const azureHealthCheck = await LLMService.testConnection();
    console.log('Azure health check result:', azureHealthCheck);

    if (azureHealthCheck.success) {
      console.log('\n4. Testing Azure chat completion...');
      const azureResponse = await LLMService.createChatCompletion([
        { role: 'user', content: 'Säg hej på svenska' }
      ], {
        maxTokens: 50
      });
      console.log('Azure Response:', azureResponse.content);
      console.log('Azure Usage:', azureResponse.usage);
    }

    // Test 3: Provider switching
    console.log('\n5. Testing provider switching back to OpenAI...');
    process.env.LLM_PROVIDER = 'openai';
    LLMProviderFactory.reset();
    const switchBackCheck = await LLMService.testConnection();
    console.log('Switch back result:', switchBackCheck);

    console.log('\n✅ All tests completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testLLMService();
