export interface ModelConfig {
  id: string;
  displayName: string;
  provider: string;
  providerModel: string;
  maxTokens: number;
  supportsStreaming: boolean;
  costPer1kTokens: number;
}

export class ModelRegistry {
  private static models: ModelConfig[] = [
    {
      id: 'gpt-4o-mini',
      displayName: 'GPT-4o Mini',
      provider: 'openai',
      providerModel: 'gpt-4o-mini',
      maxTokens: 128000,
      supportsStreaming: true,
      costPer1kTokens: 0.00015
    },
    {
      id: 'o3-mini',
      displayName: 'o3 Mini',
      provider: 'openai',
      providerModel: 'o3-mini',
      maxTokens: 65536,
      supportsStreaming: false,
      costPer1kTokens: 0.003
    },
    {
      id: 'gpt-4o-mini',
      displayName: 'GPT-4o Mini (Azure)',
      provider: 'azure',
      providerModel: process.env.AZURE_MODEL_GPT4O_MINI || 'gpt-4o-mini',
      maxTokens: 128000,
      supportsStreaming: true,
      costPer1kTokens: 0.00015
    },
    {
      id: 'o3-mini',
      displayName: 'o3 Mini (Azure)',
      provider: 'azure',
      providerModel: process.env.AZURE_MODEL_O3_MINI || 'o3-mini',
      maxTokens: 65536,
      supportsStreaming: false,
      costPer1kTokens: 0.003
    }
  ];

  static getModel(id: string, provider?: string): ModelConfig | undefined {
    console.log(`🔍 Looking for model: id="${id}", provider="${provider}"`);
    const result = this.models.find(m =>
      m.id === id && (provider ? m.provider === provider : true)
    );
    console.log(`📋 Found model:`, result ? `${result.displayName} (${result.provider})` : 'Not found');
    return result;
  }

  static getModelsForProvider(provider: string): ModelConfig[] {
    return this.models.filter(m => m.provider === provider);
  }

  static getAllModels(): ModelConfig[] {
    return [...this.models];
  }
}
